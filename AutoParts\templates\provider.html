{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Providers" %} - Auto Parts Management{% endblock %}

{% block extra_css %}
<!-- Custom styles for this page -->
<link href="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.css' %}" rel="stylesheet">

<style>
    /* Enhanced page styling matching Products page */
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1.5rem !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .page-heading::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 112, 243, 0.05), transparent);
        animation: page-shine 4s infinite;
    }

    @keyframes page-shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .btn-add-provider {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.75rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        color: white !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.3) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .btn-add-provider::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-add-provider:hover::before {
        left: 100%;
    }

    .btn-add-provider:hover {
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 10px 30px rgba(0, 112, 243, 0.5) !important;
        color: white !important;
    }

    /* Enhanced table styling */
    .table-container {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        overflow: hidden !important;
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-radius: 0.5rem !important;
    }

    /* Modal enhancements - Complete fix for interaction issues */
    .modal {
        z-index: 9999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: auto !important;
        background-color: rgba(10, 25, 47, 0.8) !important;
    }

    .modal-backdrop {
        display: none !important;
    }

    .modal-dialog {
        z-index: 10000 !important;
        position: relative !important;
        margin: 50px auto !important;
        pointer-events: auto !important;
    }

    .modal-content {
        z-index: 10001 !important;
        position: relative !important;
        pointer-events: auto !important;
        margin: 0 auto !important;
    }

    .modal-content .form-control {
        pointer-events: auto !important;
        z-index: 1080 !important;
        position: relative !important;
    }

    .modal-content .form-control:focus {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: #0070f3 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 112, 243, 0.25) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        z-index: 1090 !important;
    }

    .modal-content .form-control::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
    }

    /* Force interaction on all modal elements */
    .modal input,
    .modal textarea,
    .modal select,
    .modal button {
        pointer-events: auto !important;
        z-index: 1060 !important;
        position: relative !important;
        -webkit-user-select: auto !important;
        -moz-user-select: auto !important;
        -ms-user-select: auto !important;
        user-select: auto !important;
    }

    /* Disable any overlay that might interfere */
    .modal-dialog {
        pointer-events: auto !important;
    }

    .modal-content {
        pointer-events: auto !important;
    }

    /* Ensure no pseudo-elements block interaction */
    .modal *::before,
    .modal *::after {
        pointer-events: none !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <div class="d-sm-flex align-items-center justify-content-between">
        <div>
            <h1 class="h3 mb-0" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important;">
                <i class="fas fa-truck fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important; animation: pulse-icon 2s infinite ease-in-out;"></i>
                {% custom_trans "Providers" %}
            </h1>
            <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7); font-size: 1rem;">
                {% custom_trans "Manage your suppliers and vendors here" %}
            </p>
        </div>
        <button type="button" class="btn btn-add-provider" onclick="openAddModal()">
            <i class="fas fa-plus-circle fa-sm fa-fw mr-2"></i>{% custom_trans "Add Provider" %}
        </button>
    </div>
</div>

<!-- Providers Table -->
<div class="card table-container shadow mb-4">
    <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(0, 112, 243, 0.2); box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1);">
        <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            <i class="fas fa-list fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: pulse-icon 2s infinite ease-in-out;"></i>
            {% custom_trans "Providers List" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-hashtag fa-fw mr-1"></i>{% custom_trans "ID" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-user fa-fw mr-1"></i>{% custom_trans "Name" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-phone fa-fw mr-1"></i>{% custom_trans "Phone" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-cogs fa-fw mr-1"></i>{% custom_trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "ID" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Name" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Phone" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Actions" %}</th>
                    </tr>
                </tfoot>
                <tbody>
                    {% for provider in providers %}
                    <tr style="color: rgba(255, 255, 255, 0.8);">
                        <td>{{ provider.id }}</td>
                        <td>
                            <i class="fas fa-building fa-fw mr-2" style="color: #0070f3;"></i>
                            {{ provider.name }}
                        </td>
                        <td>
                            <i class="fas fa-phone-alt fa-fw mr-2" style="color: #00c6ff;"></i>
                            {{ provider.phone }}
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm edit-provider"
                                data-id="{{ provider.id }}"
                                data-name="{{ provider.name }}"
                                data-phone="{{ provider.phone }}"
                                style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; border-radius: 0.5rem; color: white; margin-right: 0.5rem;">
                                <i class="fas fa-edit fa-fw"></i> {% custom_trans "Edit" %}
                            </button>
                            <button type="button" class="btn btn-sm delete-provider"
                                data-id="{{ provider.id }}"
                                style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); border: none; border-radius: 0.5rem; color: white;">
                                <i class="fas fa-trash fa-fw"></i> {% custom_trans "Delete" %}
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Provider Modal -->
<div class="modal fade" id="editProviderModal" tabindex="-1" role="dialog" aria-labelledby="editProviderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="background: rgba(10, 25, 47, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 1rem; backdrop-filter: blur(20px);">
            <div class="modal-header" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border-radius: 1rem 1rem 0 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="editProviderModalLabel" style="color: white; font-weight: 600;">
                    <i class="fas fa-edit fa-fw mr-2"></i>{% custom_trans "Edit Provider" %}
                </h5>
                <button class="close" type="button" onclick="closeEditModal()" aria-label="Close" style="color: white; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{% url 'provider_edit' %}" method="post">
                {% csrf_token %}
                <div class="modal-body" style="background: transparent; color: rgba(255, 255, 255, 0.9);">
                    <input type="hidden" name="id" id="edit-provider-id">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="edit-provider-name" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-building fa-fw mr-1"></i>{% custom_trans "Provider Name" %}
                            </label>
                            <input type="text" class="form-control" id="edit-provider-name" name="name" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="edit-provider-phone" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-phone fa-fw mr-1"></i>{% custom_trans "Phone Number" %}
                            </label>
                            <input type="text" class="form-control" id="edit-provider-phone" name="phone" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: transparent; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button class="btn btn-secondary" type="button" onclick="closeEditModal()"
                            style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white;">
                        <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Cancel" %}
                    </button>
                    <button type="submit" class="btn btn-primary"
                            style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border: none; border-radius: 0.5rem; font-weight: 600;">
                        <i class="fas fa-save fa-fw mr-1"></i>{% custom_trans "Save Changes" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Provider Modal -->
<div class="modal fade" id="addProviderModal" tabindex="-1" role="dialog" aria-labelledby="addProviderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="background: rgba(10, 25, 47, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 1rem; backdrop-filter: blur(20px);">
            <div class="modal-header" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border-radius: 1rem 1rem 0 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="addProviderModalLabel" style="color: white; font-weight: 600;">
                    <i class="fas fa-plus-circle fa-fw mr-2"></i>{% custom_trans "Add Provider" %}
                </h5>
                <button class="close" type="button" onclick="closeAddModal()" aria-label="Close" style="color: white; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{% url 'provider_add' %}" method="post">
                {% csrf_token %}
                <div class="modal-body" style="background: transparent; color: rgba(255, 255, 255, 0.9);">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="add-provider-name" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-building fa-fw mr-1"></i>{% custom_trans "Provider Name" %}
                            </label>
                            <input type="text" class="form-control" id="add-provider-name" name="name" required
                                   placeholder="{% custom_trans 'Enter provider name...' %}"
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="add-provider-phone" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-phone fa-fw mr-1"></i>{% custom_trans "Phone Number" %}
                            </label>
                            <input type="text" class="form-control" id="add-provider-phone" name="phone" required
                                   placeholder="{% custom_trans 'Enter phone number...' %}"
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: transparent; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button class="btn btn-secondary" type="button" onclick="closeAddModal()"
                            style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white;">
                        <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Cancel" %}
                    </button>
                    <button type="submit" class="btn btn-primary"
                            style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border: none; border-radius: 0.5rem; font-weight: 600;">
                        <i class="fas fa-plus-circle fa-fw mr-1"></i>{% custom_trans "Add Provider" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Provider Modal -->
<div class="modal fade" id="deleteProviderModal" tabindex="-1" role="dialog" aria-labelledby="deleteProviderModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="background: rgba(10, 25, 47, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 1rem; backdrop-filter: blur(20px);">
            <div class="modal-header" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); border-radius: 1rem 1rem 0 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="deleteProviderModalLabel" style="color: white; font-weight: 600;">
                    <i class="fas fa-exclamation-triangle fa-fw mr-2"></i>{% custom_trans "Delete Provider" %}
                </h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="background: transparent; color: rgba(255, 255, 255, 0.9); text-align: center; padding: 2rem;">
                <i class="fas fa-exclamation-circle fa-3x mb-3" style="color: #dc3545;"></i>
                <p style="font-size: 1.1rem; margin-bottom: 1rem;">
                    {% custom_trans "Are you sure you want to delete this provider?" %}
                </p>
                <p style="color: rgba(255, 255, 255, 0.6);">
                    {% custom_trans "This action cannot be undone." %}
                </p>
            </div>
            <div class="modal-footer" style="background: transparent; border-top: 1px solid rgba(255, 255, 255, 0.1); justify-content: center;">
                <button class="btn btn-secondary" type="button" data-dismiss="modal"
                        style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white; margin-right: 1rem;">
                    <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Cancel" %}
                </button>
                <form action="{% url 'provider_remove' %}" method="post" style="display: inline;">
                    {% csrf_token %}
                    <input type="hidden" name="id" id="delete-provider-id">
                    <button type="submit" class="btn btn-danger"
                            style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); border: none; border-radius: 0.5rem; font-weight: 600;">
                        <i class="fas fa-trash fa-fw mr-1"></i>{% custom_trans "Delete" %}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Page level plugins -->
<script src="{% static 'sbadmin/vendor/datatables/jquery.dataTables.min.js' %}"></script>
<script src="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.js' %}"></script>

<!-- Page level custom scripts -->
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#dataTable').DataTable();

        // Force remove any existing backdrops on page load
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Edit Provider
        $('.edit-provider').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var phone = $(this).data('phone');

            openEditModal(id, name, phone);
        });

        // Delete Provider
        $('.delete-provider').click(function() {
            var id = $(this).data('id');
            $('#delete-provider-id').val(id);
            $('#deleteProviderModal').modal('show');
        });

        // Complete fix for modal interaction issues
        $('#addProviderModal').on('show.bs.modal', function () {
            // Remove any existing backdrop
            $('.modal-backdrop').remove();

            // Ensure modal is fully interactive
            $(this).css({
                'pointer-events': 'auto',
                'z-index': '9999',
                'position': 'fixed',
                'top': '0',
                'left': '0',
                'width': '100%',
                'height': '100%',
                'background-color': 'rgba(10, 25, 47, 0.8)',
                'overflow': 'auto'
            });
        });

        $('#addProviderModal').on('shown.bs.modal', function () {
            // Ensure all form elements are interactive
            $(this).find('input, textarea, select, button').css({
                'pointer-events': 'auto',
                'z-index': '10001',
                'position': 'relative'
            });

            // Focus on first input
            setTimeout(function() {
                $('#add-provider-name').focus();
            }, 100);
        });

        // Fix backdrop click for Add Modal
        $('#addProviderModal').on('click', function(e) {
            if (e.target === this) {
                $(this).modal('hide');
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }
        });

        // Fix Edit Provider Modal interaction issues
        $('#editProviderModal').on('show.bs.modal', function () {
            // Remove any existing backdrop
            $('.modal-backdrop').remove();

            // Ensure modal is fully interactive
            $(this).css({
                'pointer-events': 'auto',
                'z-index': '9999',
                'position': 'fixed',
                'top': '0',
                'left': '0',
                'width': '100%',
                'height': '100%',
                'background-color': 'rgba(10, 25, 47, 0.8)',
                'overflow': 'auto'
            });
        });

        $('#editProviderModal').on('shown.bs.modal', function () {
            // Ensure all form elements are interactive
            $(this).find('input, textarea, select, button').css({
                'pointer-events': 'auto',
                'z-index': '10001',
                'position': 'relative'
            });

            // Focus on first input
            setTimeout(function() {
                $('#edit-provider-name').focus();
            }, 100);
        });

        // Force enable all inputs on modal show
        $('#addProviderModal').on('show.bs.modal', function () {
            $(this).find('input, textarea, select, button').each(function() {
                $(this).prop('disabled', false);
                $(this).css({
                    'pointer-events': 'auto',
                    'z-index': '10001'
                });
            });
        });

        // Force enable all inputs on edit modal show
        $('#editProviderModal').on('show.bs.modal', function () {
            $(this).find('input, textarea, select, button').each(function() {
                $(this).prop('disabled', false);
                $(this).css({
                    'pointer-events': 'auto',
                    'z-index': '10001'
                });
            });
        });

        // Add click handlers for modal buttons
        $(document).on('click', '[data-target="#addProviderModal"]', function(e) {
            e.preventDefault();
            $('.modal-backdrop').remove();
            $('#addProviderModal').modal('show');
        });

        // Ensure close buttons work
        $(document).on('click', '.modal .close, .modal [data-dismiss="modal"]', function(e) {
            e.preventDefault();
            $(this).closest('.modal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });

        // Ensure proper cleanup when modals are hidden
        $('#addProviderModal, #editProviderModal').on('hidden.bs.modal', function () {
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');
        });
    });

    // Custom modal functions
    function openAddModal() {
        // Remove any existing backdrop
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Show modal with custom method
        $('#addProviderModal').css({
            'display': 'block',
            'z-index': '9999',
            'position': 'fixed',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(10, 25, 47, 0.8)',
            'overflow': 'auto'
        }).addClass('show');

        // Ensure all elements are interactive
        $('#addProviderModal input, #addProviderModal textarea, #addProviderModal button').css({
            'pointer-events': 'auto',
            'z-index': '10001'
        });

        // Focus first input
        setTimeout(function() {
            $('#add-provider-name').focus();
        }, 100);
    }

    function closeAddModal() {
        $('#addProviderModal').css('display', 'none').removeClass('show');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    }

    function openEditModal(id, name, phone) {
        // Remove any existing backdrop
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Fill form data
        $('#edit-provider-id').val(id);
        $('#edit-provider-name').val(name);
        $('#edit-provider-phone').val(phone);

        // Show modal with custom method
        $('#editProviderModal').css({
            'display': 'block',
            'z-index': '9999',
            'position': 'fixed',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(10, 25, 47, 0.8)',
            'overflow': 'auto'
        }).addClass('show');

        // Ensure all elements are interactive
        $('#editProviderModal input, #editProviderModal textarea, #editProviderModal button').css({
            'pointer-events': 'auto',
            'z-index': '10001'
        });

        // Focus first input
        setTimeout(function() {
            $('#edit-provider-name').focus();
        }, 100);
    }

    function closeEditModal() {
        $('#editProviderModal').css('display', 'none').removeClass('show');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    }
</script>
{% endblock %}
