{% extends 'base.html' %}

{% block title %}Dashboard - Auto Parts Management{% endblock %}
{% load static %}
{% block content %}
<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Products Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Products</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ products_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Providers Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Providers</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ providers_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-truck fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Purchasers Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Purchasers</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ purchasers_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Purchases Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Purchases</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ purchases_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Expenses Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Expenses</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ expenses_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Tools Row -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-tools fa-fw mr-2"></i>{% custom_trans "Quick Tools" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'qr_scanner_view' %}" class="btn btn-outline-primary btn-block h-100 d-flex align-items-center justify-content-center" style="min-height: 80px;">
                            <div class="text-center">
                                <i class="fas fa-qrcode fa-2x mb-2"></i>
                                <div>{% custom_trans "QR Scanner" %}</div>
                                <small class="text-muted">{% custom_trans "Scan QR codes" %}</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'product_add' %}" class="btn btn-outline-success btn-block h-100 d-flex align-items-center justify-content-center" style="min-height: 80px;">
                            <div class="text-center">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <div>{% custom_trans "Add Product" %}</div>
                                <small class="text-muted">{% custom_trans "Quick add" %}</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'purchases_add_view' %}" class="btn btn-outline-info btn-block h-100 d-flex align-items-center justify-content-center" style="min-height: 80px;">
                            <div class="text-center">
                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                <div>{% custom_trans "New Purchase" %}</div>
                                <small class="text-muted">{% custom_trans "Record sale" %}</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{% url 'expense_add_view' %}" class="btn btn-outline-warning btn-block h-100 d-flex align-items-center justify-content-center" style="min-height: 80px;">
                            <div class="text-center">
                                <i class="fas fa-money-bill-alt fa-2x mb-2"></i>
                                <div>{% custom_trans "Add Expense" %}</div>
                                <small class="text-muted">{% custom_trans "Track costs" %}</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Financial Summary Card with Percent Circle Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Financial Summary</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container" style="position: relative; height:200px; width:200px; margin: 0 auto;">
                            <canvas id="financialChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mt-4 text-center small">
                            <span class="mr-2">
                                <i class="fas fa-circle text-danger"></i> Expenses: {{ formatted_total_expenses }}
                            </span>
                            <br>
                            <span class="mr-2">
                                <i class="fas fa-circle text-success"></i> Purchaser Amounts: {{ formatted_total_purchaser_amounts }}
                            </span>
                            <br>
                            <span class="mr-2">
                                <i class="fas fa-circle text-info"></i> Difference: {{ formatted_difference }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <div class="progress mb-2">
                        <div class="progress-bar bg-danger" role="progressbar" style="width: {{ expenses_percentage }}%"
                            aria-valuenow="{{ expenses_percentage }}" aria-valuemin="0" aria-valuemax="100">Expenses: {{ expenses_percentage }}%</div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ purchaser_percentage }}%"
                            aria-valuenow="{{ purchaser_percentage }}" aria-valuemin="0" aria-valuemax="100">Purchaser Amounts: {{ purchaser_percentage }}%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Card -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'product_view' %}" class="btn btn-primary btn-block">
                            <i class="fas fa-box fa-sm fa-fw mr-2"></i>Manage Products
                        </a>
                    </div>
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'provider_view' %}" class="btn btn-success btn-block">
                            <i class="fas fa-truck fa-sm fa-fw mr-2"></i>Manage Providers
                        </a>
                    </div>
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'purchaser_view' %}" class="btn btn-info btn-block">
                            <i class="fas fa-users fa-sm fa-fw mr-2"></i>Manage Purchasers
                        </a>
                    </div>
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'purchases_view' %}" class="btn btn-warning btn-block">
                            <i class="fas fa-shopping-cart fa-sm fa-fw mr-2"></i>Manage Purchases
                        </a>
                    </div>
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'expense_view' %}" class="btn btn-danger btn-block">
                            <i class="fas fa-money-bill-alt fa-sm fa-fw mr-2"></i>Manage Expenses
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Info Card -->
<div class="row">
    <div class="col-lg-12 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
            </div>
            <div class="card-body">
                <p>Welcome to the Auto Parts Management System. This dashboard provides an overview of your inventory and transactions.</p>
                <p>Use the sidebar to navigate to different sections of the application.</p>
                <p>Use the quick actions above to add new items to your inventory.</p>
                <p>The Financial Summary chart shows the relationship between your expenses and purchaser amounts. The difference represents potential profit or loss.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Page level plugins -->
<script src="{% static 'sbadmin/vendor/chart.js/Chart.min.js' %}"></script>

<!-- Financial Chart -->
<script>
    // Set new default font family and font color to mimic Bootstrap's default styling
    Chart.defaults.global.defaultFontFamily = 'Nunito', '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
    Chart.defaults.global.defaultFontColor = '#858796';

    // Financial Chart
    var ctx = document.getElementById("financialChart");
    var financialChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ["Expenses", "Purchaser Amounts", "Difference"],
            datasets: [{
                data: [{{ total_expenses }}, {{ total_purchaser_amounts }}, Math.max(0, {{ difference }})],
                backgroundColor: ['#e74a3b', '#1cc88a', '#36b9cc'],
                hoverBackgroundColor: ['#be3c2e', '#17a673', '#2c9faf'],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }],
        },
        options: {
            maintainAspectRatio: false,
            tooltips: {
                backgroundColor: "rgb(255,255,255)",
                bodyFontColor: "#858796",
                borderColor: '#dddfeb',
                borderWidth: 1,
                xPadding: 15,
                yPadding: 15,
                displayColors: false,
                caretPadding: 10,
                callbacks: {
                    label: function(tooltipItem, data) {
                        var label = data.labels[tooltipItem.index];
                        var value = data.datasets[0].data[tooltipItem.index];
                        return label + ': $' + value.toFixed(2);
                    }
                }
            },
            legend: {
                display: false
            },
            cutoutPercentage: 70,
        },
    });
</script>
{% endblock %}
