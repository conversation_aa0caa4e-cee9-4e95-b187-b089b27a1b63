{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}Dashboard - Auto Parts Management{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard specific enhancements */
    .card-stats {
        background: rgba(10, 25, 47, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 1rem !important;
        backdrop-filter: blur(10px) !important;
        transition: all 0.3s ease !important;
        overflow: hidden !important;
        position: relative !important;
    }

    .card-stats::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
    }

    .card-stats:hover {
        transform: translateY(-5px) scale(1.02) !important;
        box-shadow: 0 15px 35px rgba(0, 112, 243, 0.3) !important;
    }

    .card-stats .card-body {
        position: relative;
        z-index: 2;
    }

    .stats-icon {
        font-size: 2.5rem !important;
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        animation: pulse-icon 2s infinite ease-in-out !important;
    }

    @keyframes pulse-icon {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .stats-number {
        font-size: 2rem !important;
        font-weight: 700 !important;
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }

    .stats-label {
        color: rgba(255, 255, 255, 0.7) !important;
        font-weight: 600 !important;
        text-transform: uppercase !important;
        letter-spacing: 1px !important;
        font-size: 0.8rem !important;
    }

    /* Chart containers */
    .chart-container {
        background: rgba(10, 25, 47, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 1rem !important;
        backdrop-filter: blur(10px) !important;
    }

    /* Enhanced page heading */
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1rem !important;
        padding: 1.5rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .page-heading h1 {
        margin: 0 !important;
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }

    /* Floating animation for cards */
    .floating-card {
        animation: float-card 6s ease-in-out infinite !important;
    }

    .floating-card:nth-child(2) { animation-delay: 1s !important; }
    .floating-card:nth-child(3) { animation-delay: 2s !important; }
    .floating-card:nth-child(4) { animation-delay: 3s !important; }

    @keyframes float-card {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Quick Actions enhancements */
    .quick-action-btn {
        padding: 1rem !important;
        font-weight: 600 !important;
        border-radius: 0.75rem !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .quick-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .quick-action-btn:hover::before {
        left: 100%;
    }

    .quick-action-btn:hover {
        transform: translateY(-3px) scale(1.05) !important;
    }

    /* Financial stats enhancements */
    .financial-stat {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        cursor: pointer !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .financial-stat::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s;
    }

    .financial-stat:hover::before {
        left: 100%;
    }

    .financial-stat:hover {
        transform: translateY(-5px) scale(1.02) !important;
        box-shadow: 0 10px 25px rgba(0, 112, 243, 0.3) !important;
    }

    /* Enhanced progress bars for financial summary */
    .progress {
        position: relative !important;
        overflow: hidden !important;
    }

    .progress .progress-bar {
        position: relative !important;
        overflow: hidden !important;
    }

    .progress .progress-bar::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: progress-shine 3s infinite;
    }

    @keyframes progress-shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* System info enhancements */
    .system-info-item {
        transition: all 0.3s ease !important;
        padding: 1rem !important;
        border-radius: 1rem !important;
        cursor: pointer !important;
    }

    .system-info-item:hover {
        background: rgba(0, 112, 243, 0.1) !important;
        transform: translateY(-5px) !important;
    }

    .info-section {
        transition: all 0.3s ease !important;
    }

    .info-section:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 112, 243, 0.2) !important;
    }

    /* Enhanced progress bars */
    .progress-bar {
        transition: all 0.3s ease !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .progress-bar::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: progress-shine 2s infinite;
    }

    @keyframes progress-shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* Blue themed card headers */
    .card-header {
        background: rgba(255, 255, 255, 0.95) !important;
        border-bottom: 1px solid rgba(0, 112, 243, 0.2) !important;
        border-radius: 1.5rem 1.5rem 0 0 !important;
        backdrop-filter: blur(10px) !important;
        box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1) !important;
    }

    .card-header h6,
    .card-header h6 *,
    .card-header .font-weight-bold,
    .card-header .font-weight-bold * {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        font-weight: 700 !important;
        text-shadow: none !important;
    }

    .card-header i {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        animation: pulse-icon 2s infinite ease-in-out !important;
    }

    /* Enhanced card header effects */
    .card-header {
        position: relative !important;
        overflow: hidden !important;
    }

    .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 112, 243, 0.1), transparent);
        animation: header-shine 3s infinite;
    }

    @keyframes header-shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .card-header:hover {
        background: rgba(255, 255, 255, 1) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 15px rgba(0, 112, 243, 0.2) !important;
    }

    .card-header:hover h6 {
        transform: scale(1.05) !important;
    }

    /* Dashboard action button styling */
    .dashboard-action-btn {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.75rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        color: white !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.3) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .dashboard-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .dashboard-action-btn:hover::before {
        left: 100%;
    }

    .dashboard-action-btn:hover {
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 10px 30px rgba(0, 112, 243, 0.5) !important;
        color: white !important;
    }

    .dashboard-action-btn:active {
        transform: translateY(-1px) scale(1.02) !important;
    }

    /* Enhanced page heading */
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1.5rem !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .page-heading::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 112, 243, 0.05), transparent);
        animation: page-shine 4s infinite;
    }

    @keyframes page-shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* Override all button colors in dashboard to blue theme */
    .btn-primary {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        color: white !important;
    }

    .btn-success {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        color: white !important;
    }

    .btn-info {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        color: white !important;
    }

    .btn-warning {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        color: white !important;
    }

    .btn-danger {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        color: white !important;
    }
</style>
{% endblock %}
{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <div class="d-sm-flex align-items-center justify-content-between">
        <div>
            <h1 class="h3 mb-0" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important;">
                <i class="fas fa-tachometer-alt fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important; animation: pulse-icon 2s infinite ease-in-out;"></i>
                {% custom_trans "Dashboard" %}
            </h1>
            <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7); font-size: 1rem;">
                {% custom_trans "Monitor your business performance with real-time statistics" %}
            </p>
        </div>
        <a href="#" class="d-none d-sm-inline-block btn btn-primary shadow-sm dashboard-action-btn">
            <i class="fas fa-download fa-sm mr-2"></i>
            {% custom_trans "Generate Report" %}
        </a>
    </div>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Products Card -->
    <div class="col-xl-3 col-md-6 mb-4 floating-card">
        <div class="card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            {% custom_trans "Products" %}
                        </div>
                        <div class="stats-number">{{ products_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Providers Card -->
    <div class="col-xl-3 col-md-6 mb-4 floating-card">
        <div class="card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            {% custom_trans "Providers" %}
                        </div>
                        <div class="stats-number">{{ providers_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-truck stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Purchasers Card -->
    <div class="col-xl-3 col-md-6 mb-4 floating-card">
        <div class="card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            {% custom_trans "Purchasers" %}
                        </div>
                        <div class="stats-number">{{ purchasers_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Purchases Card -->
    <div class="col-xl-3 col-md-6 mb-4 floating-card">
        <div class="card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            {% custom_trans "Purchases" %}
                        </div>
                        <div class="stats-number">{{ purchases_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Expenses Card -->
    <div class="col-xl-3 col-md-6 mb-4 floating-card">
        <div class="card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            {% custom_trans "Expenses" %}
                        </div>
                        <div class="stats-number">{{ expenses_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-alt stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Content Row -->
<div class="row">
    <!-- Financial Summary Card with Percent Circle Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card chart-container shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-chart-pie fa-fw mr-2"></i>{% custom_trans "Financial Summary" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container" style="position: relative; height:200px; width:200px; margin: 0 auto;">
                            <canvas id="financialChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mt-4 text-center small">
                            <div class="financial-stat mb-3" style="background: linear-gradient(135deg, rgba(0, 112, 243, 0.2) 0%, rgba(0, 112, 243, 0.1) 100%); padding: 1rem; border-radius: 0.75rem; border: 2px solid rgba(0, 112, 243, 0.3); backdrop-filter: blur(10px);">
                                <i class="fas fa-money-bill-alt mr-2" style="color: #0070f3; font-size: 1.2rem;"></i>
                                <div style="color: #0070f3; font-weight: 700; font-size: 0.8rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">
                                    {% custom_trans "Expenses" %}
                                </div>
                                <div style="color: rgba(255, 255, 255, 0.9); font-weight: 700; font-size: 1.1rem;">
                                    {{ formatted_total_expenses }}
                                </div>
                            </div>
                            <div class="financial-stat mb-3" style="background: linear-gradient(135deg, rgba(0, 198, 255, 0.2) 0%, rgba(0, 198, 255, 0.1) 100%); padding: 1rem; border-radius: 0.75rem; border: 2px solid rgba(0, 198, 255, 0.3); backdrop-filter: blur(10px);">
                                <i class="fas fa-hand-holding-usd mr-2" style="color: #00c6ff; font-size: 1.2rem;"></i>
                                <div style="color: #00c6ff; font-weight: 700; font-size: 0.8rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">
                                    {% custom_trans "Purchaser Amounts" %}
                                </div>
                                <div style="color: rgba(255, 255, 255, 0.9); font-weight: 700; font-size: 1.1rem;">
                                    {{ formatted_total_purchaser_amounts }}
                                </div>
                            </div>
                            <div class="financial-stat" style="background: linear-gradient(135deg, rgba(28, 200, 138, 0.2) 0%, rgba(28, 200, 138, 0.1) 100%); padding: 1rem; border-radius: 0.75rem; border: 2px solid rgba(28, 200, 138, 0.3); backdrop-filter: blur(10px);">
                                <i class="fas fa-chart-line mr-2" style="color: #1cc88a; font-size: 1.2rem;"></i>
                                <div style="color: #1cc88a; font-weight: 700; font-size: 0.8rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 0.5rem;">
                                    {% custom_trans "Difference" %}
                                </div>
                                <div style="color: rgba(255, 255, 255, 0.9); font-weight: 700; font-size: 1.1rem;">
                                    {{ formatted_difference }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span style="color: #0070f3; font-weight: 600; font-size: 0.9rem;">
                                <i class="fas fa-money-bill-alt fa-fw mr-1"></i>{% custom_trans "Expenses" %}
                            </span>
                            <span style="color: rgba(255, 255, 255, 0.8); font-weight: 600; font-size: 0.9rem;">
                                {{ expenses_percentage }}%
                            </span>
                        </div>
                        <div class="progress" style="height: 1.2rem; background: rgba(0, 112, 243, 0.1); border-radius: 1rem; border: 1px solid rgba(0, 112, 243, 0.2);">
                            <div class="progress-bar" role="progressbar" style="width: {{ expenses_percentage }}%; background: linear-gradient(135deg, #0070f3 0%, #0056b3 100%); border-radius: 1rem; box-shadow: 0 2px 10px rgba(0, 112, 243, 0.3);"
                                aria-valuenow="{{ expenses_percentage }}" aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span style="color: #00c6ff; font-weight: 600; font-size: 0.9rem;">
                                <i class="fas fa-hand-holding-usd fa-fw mr-1"></i>{% custom_trans "Purchaser Amounts" %}
                            </span>
                            <span style="color: rgba(255, 255, 255, 0.8); font-weight: 600; font-size: 0.9rem;">
                                {{ purchaser_percentage }}%
                            </span>
                        </div>
                        <div class="progress" style="height: 1.2rem; background: rgba(0, 198, 255, 0.1); border-radius: 1rem; border: 1px solid rgba(0, 198, 255, 0.2);">
                            <div class="progress-bar" role="progressbar" style="width: {{ purchaser_percentage }}%; background: linear-gradient(135deg, #00c6ff 0%, #00a8d6 100%); border-radius: 1rem; box-shadow: 0 2px 10px rgba(0, 198, 255, 0.3);"
                                aria-valuenow="{{ purchaser_percentage }}" aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Card -->
    <div class="col-lg-6 mb-4">
        <div class="card chart-container shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-bolt fa-fw mr-2"></i>{% custom_trans "Quick Actions" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6 mb-3">
                        <a href="{% url 'product_view' %}" class="btn btn-primary btn-block quick-action-btn">
                            <i class="fas fa-box fa-fw mr-2"></i>{% custom_trans "Manage Products" %}
                        </a>
                    </div>
                    <div class="col-lg-6 mb-3">
                        <a href="{% url 'provider_view' %}" class="btn btn-success btn-block quick-action-btn">
                            <i class="fas fa-truck fa-fw mr-2"></i>{% custom_trans "Manage Providers" %}
                        </a>
                    </div>
                    <div class="col-lg-6 mb-3">
                        <a href="{% url 'purchaser_view' %}" class="btn btn-info btn-block quick-action-btn">
                            <i class="fas fa-users fa-fw mr-2"></i>{% custom_trans "Manage Purchasers" %}
                        </a>
                    </div>
                    <div class="col-lg-6 mb-3">
                        <a href="{% url 'purchases_view' %}" class="btn btn-warning btn-block quick-action-btn">
                            <i class="fas fa-shopping-cart fa-fw mr-2"></i>{% custom_trans "Manage Purchases" %}
                        </a>
                    </div>
                    <div class="col-lg-12 mb-2">
                        <a href="{% url 'expense_view' %}" class="btn btn-danger btn-block quick-action-btn">
                            <i class="fas fa-money-bill-alt fa-fw mr-2"></i>{% custom_trans "Manage Expenses" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Info Card -->
<div class="row">
    <div class="col-lg-12 mb-4">
        <div class="card chart-container shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-info-circle fa-fw mr-2"></i>{% custom_trans "System Information" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-4">
                        <div class="system-info-item">
                            <i class="fas fa-car fa-3x mb-3" style="color: #0070f3; animation: pulse-icon 2s infinite ease-in-out;"></i>
                            <h5 style="color: rgba(255, 255, 255, 0.9);">{% custom_trans "Auto Parts Management" %}</h5>
                            <p style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">{% custom_trans "Complete inventory solution" %}</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="system-info-item">
                            <i class="fas fa-chart-line fa-3x mb-3" style="color: #00c6ff; animation: pulse-icon 2s infinite ease-in-out; animation-delay: 0.5s;"></i>
                            <h5 style="color: rgba(255, 255, 255, 0.9);">{% custom_trans "Real-time Analytics" %}</h5>
                            <p style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">{% custom_trans "Track your business performance" %}</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="system-info-item">
                            <i class="fas fa-shield-alt fa-3x mb-3" style="color: #1cc88a; animation: pulse-icon 2s infinite ease-in-out; animation-delay: 1s;"></i>
                            <h5 style="color: rgba(255, 255, 255, 0.9);">{% custom_trans "Secure Platform" %}</h5>
                            <p style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">{% custom_trans "Your data is protected" %}</p>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-4">
                        <div class="system-info-item">
                            <i class="fas fa-mobile-alt fa-3x mb-3" style="color: #f6c23e; animation: pulse-icon 2s infinite ease-in-out; animation-delay: 1.5s;"></i>
                            <h5 style="color: rgba(255, 255, 255, 0.9);">{% custom_trans "Mobile Friendly" %}</h5>
                            <p style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">{% custom_trans "Access anywhere, anytime" %}</p>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="info-section" style="background: rgba(0, 112, 243, 0.1); padding: 1.5rem; border-radius: 1rem; border-left: 4px solid #0070f3;">
                            <h6 style="color: #00c6ff; margin-bottom: 1rem;">
                                <i class="fas fa-lightbulb fa-fw mr-2"></i>{% custom_trans "Getting Started" %}
                            </h6>
                            <ul style="color: rgba(255, 255, 255, 0.8); margin: 0; padding-left: 1.5rem;">
                                <li class="mb-2">{% custom_trans "Use the sidebar to navigate to different sections of the application" %}</li>
                                <li class="mb-2">{% custom_trans "Use the quick actions above to manage your inventory efficiently" %}</li>
                                <li class="mb-2">{% custom_trans "The Financial Summary chart shows the relationship between expenses and revenue" %}</li>
                                <li>{% custom_trans "Monitor your business performance with real-time statistics" %}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Page level plugins -->
<script src="{% static 'sbadmin/vendor/chart.js/Chart.min.js' %}"></script>

<!-- Financial Chart -->
<script>
    // Set new default font family and font color for modern blue theme
    Chart.defaults.global.defaultFontFamily = 'Poppins', '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
    Chart.defaults.global.defaultFontColor = 'rgba(255, 255, 255, 0.8)';

    // Financial Chart with Blue Theme
    var ctx = document.getElementById("financialChart");
    var financialChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ["{% custom_trans 'Expenses' %}", "{% custom_trans 'Purchaser Amounts' %}", "{% custom_trans 'Difference' %}"],
            datasets: [{
                data: [{{ total_expenses }}, {{ total_purchaser_amounts }}, Math.max(0, {{ difference }})],
                backgroundColor: [
                    'rgba(0, 112, 243, 0.8)',  // Primary blue for expenses
                    'rgba(0, 198, 255, 0.8)',  // Secondary blue for purchaser amounts
                    'rgba(28, 200, 138, 0.8)'  // Green for difference
                ],
                hoverBackgroundColor: [
                    'rgba(0, 112, 243, 1)',    // Solid primary blue on hover
                    'rgba(0, 198, 255, 1)',    // Solid secondary blue on hover
                    'rgba(28, 200, 138, 1)'    // Solid green on hover
                ],
                hoverBorderColor: "rgba(255, 255, 255, 0.8)",
                borderWidth: 3,
                borderColor: "rgba(255, 255, 255, 0.2)",
            }],
        },
        options: {
            maintainAspectRatio: false,
            tooltips: {
                backgroundColor: "rgba(10, 25, 47, 0.95)",
                titleFontColor: "rgba(255, 255, 255, 0.9)",
                bodyFontColor: "rgba(255, 255, 255, 0.8)",
                borderColor: 'rgba(0, 112, 243, 0.5)',
                borderWidth: 2,
                cornerRadius: 10,
                xPadding: 15,
                yPadding: 15,
                displayColors: true,
                caretPadding: 10,
                titleFontFamily: 'Poppins',
                bodyFontFamily: 'Poppins',
                callbacks: {
                    label: function(tooltipItem, data) {
                        var label = data.labels[tooltipItem.index];
                        var value = data.datasets[0].data[tooltipItem.index];
                        return label + ': $' + value.toFixed(2);
                    }
                }
            },
            legend: {
                display: false
            },
            cutoutPercentage: 60,
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2500,
                easing: 'easeOutQuart'
            },
            elements: {
                arc: {
                    borderWidth: 3,
                    hoverBorderWidth: 5
                }
            },
            plugins: {
                beforeDraw: function(chart) {
                    var ctx = chart.chart.ctx;
                    ctx.save();
                    var centerX = (chart.chartArea.left + chart.chartArea.right) / 2;
                    var centerY = (chart.chartArea.top + chart.chartArea.bottom) / 2;

                    // Add glow effect
                    ctx.shadowColor = 'rgba(0, 112, 243, 0.3)';
                    ctx.shadowBlur = 20;
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 0;

                    // Draw center circle with gradient
                    var gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 50);
                    gradient.addColorStop(0, 'rgba(0, 112, 243, 0.1)');
                    gradient.addColorStop(1, 'rgba(0, 198, 255, 0.05)');

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, 45, 0, 2 * Math.PI);
                    ctx.fillStyle = gradient;
                    ctx.fill();

                    ctx.restore();
                }
            }
        },
    });

    // Add hover effects to chart
    ctx.addEventListener('mousemove', function(e) {
        financialChart.update();
    });

    // Add click animation to financial stats
    document.querySelectorAll('.financial-stat').forEach(function(stat) {
        stat.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateX(5px)';
            }, 150);
        });
    });

    // Add click animation to system info items
    document.querySelectorAll('.system-info-item').forEach(function(item) {
        item.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-5px)';
            }, 150);
        });
    });
</script>
{% endblock %}
