{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}Dashboard - Auto Parts Management{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard specific enhancements */
    .card-stats {
        background: rgba(10, 25, 47, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 1rem !important;
        backdrop-filter: blur(10px) !important;
        transition: all 0.3s ease !important;
        overflow: hidden !important;
        position: relative !important;
    }

    .card-stats::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
    }

    .card-stats:hover {
        transform: translateY(-5px) scale(1.02) !important;
        box-shadow: 0 15px 35px rgba(0, 112, 243, 0.3) !important;
    }

    .card-stats .card-body {
        position: relative;
        z-index: 2;
    }

    .stats-icon {
        font-size: 2.5rem !important;
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        animation: pulse-icon 2s infinite ease-in-out !important;
    }

    @keyframes pulse-icon {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .stats-number {
        font-size: 2rem !important;
        font-weight: 700 !important;
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }

    .stats-label {
        color: rgba(255, 255, 255, 0.7) !important;
        font-weight: 600 !important;
        text-transform: uppercase !important;
        letter-spacing: 1px !important;
        font-size: 0.8rem !important;
    }

    /* Chart containers */
    .chart-container {
        background: rgba(10, 25, 47, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 1rem !important;
        backdrop-filter: blur(10px) !important;
    }

    /* Enhanced page heading */
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1rem !important;
        padding: 1.5rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .page-heading h1 {
        margin: 0 !important;
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }

    /* Floating animation for cards */
    .floating-card {
        animation: float-card 6s ease-in-out infinite !important;
    }

    .floating-card:nth-child(2) { animation-delay: 1s !important; }
    .floating-card:nth-child(3) { animation-delay: 2s !important; }
    .floating-card:nth-child(4) { animation-delay: 3s !important; }

    @keyframes float-card {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
</style>
{% endblock %}
{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <div class="d-sm-flex align-items-center justify-content-between">
        <h1 class="h3 mb-0">
            <i class="fas fa-tachometer-alt fa-fw mr-2"></i>
            {% custom_trans "Dashboard" %}
        </h1>
        <a href="#" class="d-none d-sm-inline-block btn btn-primary shadow-sm">
            <i class="fas fa-download fa-sm mr-2"></i>
            {% custom_trans "Generate Report" %}
        </a>
    </div>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Products Card -->
    <div class="col-xl-3 col-md-6 mb-4 floating-card">
        <div class="card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            {% custom_trans "Products" %}
                        </div>
                        <div class="stats-number">{{ products_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Providers Card -->
    <div class="col-xl-3 col-md-6 mb-4 floating-card">
        <div class="card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            {% custom_trans "Providers" %}
                        </div>
                        <div class="stats-number">{{ providers_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-truck stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Purchasers Card -->
    <div class="col-xl-3 col-md-6 mb-4 floating-card">
        <div class="card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            {% custom_trans "Purchasers" %}
                        </div>
                        <div class="stats-number">{{ purchasers_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Purchases Card -->
    <div class="col-xl-3 col-md-6 mb-4 floating-card">
        <div class="card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="stats-label mb-1">
                            {% custom_trans "Purchases" %}
                        </div>
                        <div class="stats-number">{{ purchases_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart stats-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Expenses Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Expenses</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ expenses_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Content Row -->
<div class="row">
    <!-- Financial Summary Card with Percent Circle Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Financial Summary</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container" style="position: relative; height:200px; width:200px; margin: 0 auto;">
                            <canvas id="financialChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mt-4 text-center small">
                            <span class="mr-2">
                                <i class="fas fa-circle text-danger"></i> Expenses: {{ formatted_total_expenses }}
                            </span>
                            <br>
                            <span class="mr-2">
                                <i class="fas fa-circle text-success"></i> Purchaser Amounts: {{ formatted_total_purchaser_amounts }}
                            </span>
                            <br>
                            <span class="mr-2">
                                <i class="fas fa-circle text-info"></i> Difference: {{ formatted_difference }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <div class="progress mb-2">
                        <div class="progress-bar bg-danger" role="progressbar" style="width: {{ expenses_percentage }}%"
                            aria-valuenow="{{ expenses_percentage }}" aria-valuemin="0" aria-valuemax="100">Expenses: {{ expenses_percentage }}%</div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ purchaser_percentage }}%"
                            aria-valuenow="{{ purchaser_percentage }}" aria-valuemin="0" aria-valuemax="100">Purchaser Amounts: {{ purchaser_percentage }}%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Card -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'product_view' %}" class="btn btn-primary btn-block">
                            <i class="fas fa-box fa-sm fa-fw mr-2"></i>Manage Products
                        </a>
                    </div>
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'provider_view' %}" class="btn btn-success btn-block">
                            <i class="fas fa-truck fa-sm fa-fw mr-2"></i>Manage Providers
                        </a>
                    </div>
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'purchaser_view' %}" class="btn btn-info btn-block">
                            <i class="fas fa-users fa-sm fa-fw mr-2"></i>Manage Purchasers
                        </a>
                    </div>
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'purchases_view' %}" class="btn btn-warning btn-block">
                            <i class="fas fa-shopping-cart fa-sm fa-fw mr-2"></i>Manage Purchases
                        </a>
                    </div>
                    <div class="col-lg-6 mb-2">
                        <a href="{% url 'expense_view' %}" class="btn btn-danger btn-block">
                            <i class="fas fa-money-bill-alt fa-sm fa-fw mr-2"></i>Manage Expenses
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Info Card -->
<div class="row">
    <div class="col-lg-12 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
            </div>
            <div class="card-body">
                <p>Welcome to the Auto Parts Management System. This dashboard provides an overview of your inventory and transactions.</p>
                <p>Use the sidebar to navigate to different sections of the application.</p>
                <p>Use the quick actions above to add new items to your inventory.</p>
                <p>The Financial Summary chart shows the relationship between your expenses and purchaser amounts. The difference represents potential profit or loss.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Page level plugins -->
<script src="{% static 'sbadmin/vendor/chart.js/Chart.min.js' %}"></script>

<!-- Financial Chart -->
<script>
    // Set new default font family and font color to mimic Bootstrap's default styling
    Chart.defaults.global.defaultFontFamily = 'Nunito', '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
    Chart.defaults.global.defaultFontColor = '#858796';

    // Financial Chart
    var ctx = document.getElementById("financialChart");
    var financialChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ["Expenses", "Purchaser Amounts", "Difference"],
            datasets: [{
                data: [{{ total_expenses }}, {{ total_purchaser_amounts }}, Math.max(0, {{ difference }})],
                backgroundColor: ['#e74a3b', '#1cc88a', '#36b9cc'],
                hoverBackgroundColor: ['#be3c2e', '#17a673', '#2c9faf'],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }],
        },
        options: {
            maintainAspectRatio: false,
            tooltips: {
                backgroundColor: "rgb(255,255,255)",
                bodyFontColor: "#858796",
                borderColor: '#dddfeb',
                borderWidth: 1,
                xPadding: 15,
                yPadding: 15,
                displayColors: false,
                caretPadding: 10,
                callbacks: {
                    label: function(tooltipItem, data) {
                        var label = data.labels[tooltipItem.index];
                        var value = data.datasets[0].data[tooltipItem.index];
                        return label + ': $' + value.toFixed(2);
                    }
                }
            },
            legend: {
                display: false
            },
            cutoutPercentage: 70,
        },
    });
</script>
{% endblock %}
