{% load static %}
{% load custom_i18n %}
<!DOCTYPE html>
<html lang="{% if request.session.django_language == 'ar' %}ar{% else %}en{% endif %}" dir="{% if request.session.django_language == 'ar' %}rtl{% else %}ltr{% endif %}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Auto Parts Management System">
    <meta name="author" content="">

    <title>{% block title %}{% custom_trans "Auto Parts Management System" %}{% endblock %}</title>

    <!-- Custom fonts for this template-->
    <link href="{% static 'sbadmin/vendor/fontawesome-free/css/all.min.css' %}" rel="stylesheet" type="text/css">
    {% if request.session.django_language == 'ar' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;800;900&display=swap" rel="stylesheet">
    {% else %}
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">
    {% endif %}

    <!-- Custom styles for this template-->
    <link href="{% static 'sbadmin/css/sb-admin-2.min.css' %}" rel="stylesheet">

    <!-- Custom Auto Parts Styles -->
    <link href="{% static 'custom.css' %}" rel="stylesheet">

    <!-- Enhanced Modern Styling - Consistent with Login/Register -->
    <style>
        /* Global enhancements - Match Login Page Style */
        body {
            font-family: {% if request.session.django_language == 'ar' %}'Cairo', sans-serif{% else %}'Poppins', sans-serif{% endif %} !important;
            background: #0a192f !important;
            color: rgba(255, 255, 255, 0.9) !important;
            position: relative !important;
            overflow-x: hidden !important;
        }

        /* Animated background particles */
        #page-top::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(-45deg, #0a192f, #112240, #1e3a8a, #3730a3);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            z-index: -2;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Floating particles */
        #page-top::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(0, 198, 255, 0.1), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(0, 112, 243, 0.1), transparent);
            background-repeat: repeat;
            background-size: 150px 150px;
            animation: float-particles 20s linear infinite;
            z-index: -1;
        }

        @keyframes float-particles {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-150px); }
        }

        /* Enhanced animations */
        .card, .btn, .form-control, .nav-link {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        /* Enhanced sidebar brand icon */
        .sidebar-brand-icon {
            animation: rotate 10s linear infinite !important;
            filter: drop-shadow(0 0 10px rgba(0, 198, 255, 0.5)) !important;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Page title enhancements */
        .h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
            background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            font-weight: 700 !important;
            text-shadow: none !important;
        }

        /* Text colors override */
        .text-gray-800, .text-gray-900 {
            background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
        }

        .text-gray-600 {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        /* Topbar dropdown enhancements */
        .dropdown-item:hover {
            background: rgba(0, 112, 243, 0.2) !important;
            color: white !important;
            transform: translateX(5px) !important;
        }

        .dropdown-item:focus {
            background: rgba(0, 112, 243, 0.2) !important;
            color: white !important;
        }

        /* Content wrapper enhancements */
        #content-wrapper {
            background: transparent !important;
            position: relative !important;
            z-index: 2 !important;
        }

        #content {
            position: relative !important;
            z-index: 3 !important;
        }

        /* Enhanced page wrapper */
        #wrapper {
            position: relative !important;
            z-index: 1 !important;
        }

        /* Footer enhancements */
        .sticky-footer {
            background: rgba(10, 25, 47, 0.8) !important;
            border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
            color: rgba(255, 255, 255, 0.7) !important;
            backdrop-filter: blur(20px) !important;
        }

        /* Loading states */
        .loading {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Enhanced focus states */
        *:focus {
            outline: 2px solid rgba(0, 112, 243, 0.5) !important;
            outline-offset: 2px !important;
        }

        /* Fix modal interaction issues globally */
        .modal,
        .modal * {
            pointer-events: auto !important;
        }

        .modal-backdrop {
            pointer-events: auto !important;
        }

        /* Ensure floating particles don't interfere */
        #page-top::before,
        #page-top::after {
            pointer-events: none !important;
            z-index: -1 !important;
        }
    </style>

    <!-- RTL Support for Arabic -->
    {% if request.session.django_language == 'ar' %}
    <style>
        body {
            font-family: 'Cairo', sans-serif !important;
            direction: rtl;
        }

        .sidebar {
            right: 0;
            left: auto;
        }

        .sidebar .nav-item .nav-link {
            text-align: right;
        }

        .sidebar .nav-item .nav-link i {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        #content-wrapper {
            margin-right: 14rem;
            margin-left: 0;
        }

        .topbar .navbar-nav .nav-item .dropdown-menu {
            right: 0;
            left: auto;
        }

        .card-header h6 i {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        .btn i {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        .form-control {
            text-align: right;
        }

        .table th, .table td {
            text-align: right;
        }

        .dropdown-menu {
            text-align: right;
        }

        .navbar-nav .dropdown-menu {
            right: 0;
            left: auto;
        }

        .sidebar-brand-icon {
            margin-left: 0.5rem;
            margin-right: 0;
        }

        .sidebar-brand-text {
            margin-right: 0.5rem;
            margin-left: 0;
        }

        .mr-2 {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
        }

        .ml-auto {
            margin-right: auto !important;
            margin-left: 0 !important;
        }

        .mr-auto {
            margin-left: auto !important;
            margin-right: 0 !important;
        }

        @media (max-width: 768px) {
            #content-wrapper {
                margin-right: 0;
            }
        }
    </style>
    {% endif %}

    <!-- Custom styles for this page -->
    {% block extra_css %}{% endblock %}
</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="{% url 'dashboard' %}">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-car-alt"></i>
                </div>
                <div class="sidebar-brand-text mx-3">{% custom_trans "Auto Parts" %}</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item {% if request.path == '/' %}active{% endif %}">
                <a class="nav-link" href="{% url 'dashboard' %}">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>{% custom_trans "Dashboard" %}</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                {% custom_trans "Inventory" %}
            </div>

            <!-- Nav Item - Products -->
            <li class="nav-item {% if 'product' in request.path %}active{% endif %}">
                <a class="nav-link" href="{% url 'product_view' %}">
                    <i class="fas fa-fw fa-box"></i>
                    <span>{% custom_trans "Products" %}</span></a>
            </li>

            <!-- Nav Item - Providers -->
            <li class="nav-item {% if 'provider' in request.path %}active{% endif %}">
                <a class="nav-link" href="{% url 'provider_view' %}">
                    <i class="fas fa-fw fa-truck"></i>
                    <span>{% custom_trans "Providers" %}</span></a>
            </li>

            <!-- Nav Item - Purchasers -->
            <li class="nav-item {% if 'purchaser' in request.path %}active{% endif %}">
                <a class="nav-link" href="{% url 'purchaser_view' %}">
                    <i class="fas fa-fw fa-users"></i>
                    <span>{% custom_trans "Purchasers" %}</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                {% custom_trans "Transactions" %}
            </div>

            <!-- Nav Item - Purchases -->
            <li class="nav-item {% if 'purchases' in request.path %}active{% endif %}">
                <a class="nav-link" href="{% url 'purchases_view' %}">
                    <i class="fas fa-fw fa-shopping-cart"></i>
                    <span>{% custom_trans "Purchases" %}</span></a>
            </li>

            <!-- Nav Item - Expenses -->
            <li class="nav-item {% if 'expense' in request.path %}active{% endif %}">
                <a class="nav-link" href="{% url 'expense_view' %}">
                    <i class="fas fa-fw fa-money-bill-alt"></i>
                    <span>{% custom_trans "Expenses" %}</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-dark topbar mb-4 static-top shadow" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; border-bottom: 1px solid rgba(255, 255, 255, 0.1);"

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3" style="color: white;">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Topbar Quick Actions -->
                    <div class="d-none d-sm-inline-block mr-auto ml-md-3 my-2 my-md-0 mw-100">
                        <div class="btn-group">
                            <button type="button" class="btn dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                                    style="background: rgba(255, 255, 255, 0.2); border: 1px solid rgba(255, 255, 255, 0.3); color: white; border-radius: 0.5rem; font-weight: 600;">
                                <i class="fas fa-plus-circle"></i> {% custom_trans "Add New" %}
                            </button>
                            <div class="dropdown-menu" style="background: rgba(10, 25, 47, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 0.75rem; backdrop-filter: blur(20px);">
                                <a class="dropdown-item" href="{% url 'product_view' %}" data-toggle="modal" data-target="#addProductModal"
                                   style="color: rgba(255, 255, 255, 0.9); padding: 0.75rem 1rem; border-radius: 0.5rem; transition: all 0.3s;">
                                    <i class="fas fa-box fa-sm fa-fw mr-2" style="color: #0070f3;"></i> {% custom_trans "Add Product" %}
                                </a>
                                <a class="dropdown-item" href="{% url 'provider_view' %}" data-toggle="modal" data-target="#addProviderModal"
                                   style="color: rgba(255, 255, 255, 0.9); padding: 0.75rem 1rem; border-radius: 0.5rem; transition: all 0.3s;">
                                    <i class="fas fa-truck fa-sm fa-fw mr-2" style="color: #00c6ff;"></i> {% custom_trans "Add Provider" %}
                                </a>
                                <a class="dropdown-item" href="{% url 'purchaser_view' %}" data-toggle="modal" data-target="#addPurchaserModal"
                                   style="color: rgba(255, 255, 255, 0.9); padding: 0.75rem 1rem; border-radius: 0.5rem; transition: all 0.3s;">
                                    <i class="fas fa-user fa-sm fa-fw mr-2" style="color: #28a745;"></i> {% custom_trans "Add Purchaser" %}
                                </a>
                                <div class="dropdown-divider" style="border-color: rgba(255, 255, 255, 0.1);"></div>
                                <a class="dropdown-item" href="{% url 'purchases_view' %}" data-toggle="modal" data-target="#addPurchaseModal"
                                   style="color: rgba(255, 255, 255, 0.9); padding: 0.75rem 1rem; border-radius: 0.5rem; transition: all 0.3s;">
                                    <i class="fas fa-shopping-cart fa-sm fa-fw mr-2" style="color: #ffc107;"></i> {% custom_trans "Add Purchase" %}
                                </a>
                                <a class="dropdown-item" href="{% url 'expense_view' %}" data-toggle="modal" data-target="#addExpenseModal"
                                   style="color: rgba(255, 255, 255, 0.9); padding: 0.75rem 1rem; border-radius: 0.5rem; transition: all 0.3s;">
                                    <i class="fas fa-money-bill-alt fa-sm fa-fw mr-2" style="color: #dc3545;"></i> {% custom_trans "Add Expense" %}
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">

                        <div class="topbar-divider d-none d-sm-block" style="border-color: rgba(255, 255, 255, 0.2);"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="color: white;">
                                <span class="mr-2 d-none d-lg-inline small" style="color: rgba(255, 255, 255, 0.9); font-weight: 600;">{{ request.user.username }}</span>
                                <i class="fas fa-user-circle fa-fw" style="color: rgba(255, 255, 255, 0.9); font-size: 1.5rem;"></i>
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown" style="background: rgba(10, 25, 47, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 0.75rem; backdrop-filter: blur(20px);">
                                <a class="dropdown-item" href="{% url 'profile_view' %}"
                                   style="color: rgba(255, 255, 255, 0.9); padding: 0.75rem 1rem; border-radius: 0.5rem; transition: all 0.3s;">
                                    <i class="fas fa-user fa-sm fa-fw mr-2" style="color: #0070f3;"></i>
                                    {% custom_trans "Profile" %}
                                </a>
                                <a class="dropdown-item" href="{% url 'settings_view' %}"
                                   style="color: rgba(255, 255, 255, 0.9); padding: 0.75rem 1rem; border-radius: 0.5rem; transition: all 0.3s;">
                                    <i class="fas fa-cogs fa-sm fa-fw mr-2" style="color: #00c6ff;"></i>
                                    {% custom_trans "Settings" %}
                                </a>
                                <div class="dropdown-divider" style="border-color: rgba(255, 255, 255, 0.1);"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal"
                                   style="color: rgba(255, 255, 255, 0.9); padding: 0.75rem 1rem; border-radius: 0.5rem; transition: all 0.3s;">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2" style="color: #dc3545;"></i>
                                    {% custom_trans "Logout" %}
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    {% if messages %}
                    <div class="row">
                        <div class="col-12">
                            {% for message in messages %}
                            <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    {% block content %}{% endblock %}

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span style="color: white; font-weight: 600;">Copyright &copy; Auto Parts Management 2025</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true" style="background-color: rgba(10, 25, 47, 0.8);">
        <div class="modal-dialog" role="document">
            <div class="modal-content" style="background: rgba(10, 25, 47, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 1rem; backdrop-filter: blur(20px);">
                <div class="modal-header" style="background: transparent; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                    <h5 class="modal-title" id="exampleModalLabel" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 600;">
                        <i class="fas fa-sign-out-alt fa-fw mr-2"></i>{% custom_trans "Ready to Leave?" %}
                    </h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close" style="color: rgba(255, 255, 255, 0.7);">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body" style="color: rgba(255, 255, 255, 0.8);">
                    {% custom_trans "Select 'Logout' below if you are ready to end your current session." %}
                </div>
                <div class="modal-footer" style="background: transparent; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal"
                            style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white;">
                        <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Cancel" %}
                    </button>
                    <a class="btn btn-primary" href="{% url 'logout_view' %}"
                       style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); border: none; border-radius: 0.5rem; color: white;">
                        <i class="fas fa-sign-out-alt fa-fw mr-1"></i>{% custom_trans "Logout" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{% static 'sbadmin/vendor/jquery/jquery.min.js' %}"></script>
    <script src="{% static 'sbadmin/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{% static 'sbadmin/vendor/jquery-easing/jquery.easing.min.js' %}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{% static 'sbadmin/js/sb-admin-2.min.js' %}"></script>

    {% block extra_js %}{% endblock %}

</body>

</html>
