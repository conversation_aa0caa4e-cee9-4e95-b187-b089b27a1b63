{% load static %}
{% load custom_i18n %}
<!DOCTYPE html>
<html lang="{% if request.session.django_language == 'ar' %}ar{% else %}en{% endif %}" dir="{% if request.session.django_language == 'ar' %}rtl{% else %}ltr{% endif %}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="{% custom_trans 'Auto Parts Management System' %}">
    <meta name="author" content="">

    <title>{% custom_trans 'Auto Parts Management System' %} - {% custom_trans 'Login' %}</title>

    <!-- Custom fonts for this template-->
    <link href="{% static 'sbadmin/vendor/fontawesome-free/css/all.min.css' %}" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;900&display=swap" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="{% static 'sbadmin/css/sb-admin-2.min.css' %}" rel="stylesheet">
    <!-- Custom styles for Auto Parts Management -->
    <link href="{% static 'custom.css' %}" rel="stylesheet">

    <style>
        body {
            font-family: {% if request.session.django_language == 'ar' %}'Cairo', sans-serif{% else %}'Poppins', sans-serif{% endif %} !important;
            direction: {% if request.session.django_language == 'ar' %}rtl{% else %}ltr{% endif %};
            margin: 0;
            overflow-x: hidden;
            background-color: #0a192f;
            height: 100vh;
        }

        .form-control {
            text-align: {% if request.session.django_language == 'ar' %}right{% else %}left{% endif %};
        }

        .btn {
            font-family: {% if request.session.django_language == 'ar' %}'Cairo', sans-serif{% else %}'Poppins', sans-serif{% endif %};
        }

        /* Enhanced Language switcher styles */
        .language-switcher {
            position: absolute;
            top: 30px;
            {% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 30px;
            z-index: 1000;
        }

        .language-toggle-container {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 50px;
            padding: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .language-toggle-container:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
        }

        .language-option {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            margin: 0 2px;
            border-radius: 40px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-width: 80px;
            justify-content: center;
        }

        .language-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .language-option:hover::before {
            opacity: 1;
        }

        .language-option:hover {
            color: white;
            transform: scale(1.05);
            text-decoration: none;
        }

        .language-option.active {
            background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 112, 243, 0.4);
            transform: scale(1.02);
        }

        .language-option.active:hover {
            transform: scale(1.08);
            box-shadow: 0 6px 20px rgba(0, 112, 243, 0.5);
        }

        .language-flag {
            font-size: 18px;
            margin-{% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 8px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .language-text {
            font-family: {% if request.session.django_language == 'ar' %}'Cairo', sans-serif{% else %}'Poppins', sans-serif{% endif %};
            letter-spacing: 0.5px;
        }

        /* Responsive design for mobile */
        @media (max-width: 768px) {
            .language-switcher {
                top: 20px;
                {% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 20px;
            }

            .language-toggle-container {
                padding: 6px;
            }

            .language-option {
                padding: 8px 12px;
                font-size: 13px;
                min-width: 70px;
            }

            .language-flag {
                font-size: 16px;
                margin-{% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 6px;
            }
        }

        /* Animation for language switch */
        .language-switch-animation {
            animation: languageSwitch 0.6s ease-in-out;
        }

        @keyframes languageSwitch {
            0% { opacity: 1; transform: translateY(0); }
            50% { opacity: 0.7; transform: translateY(-10px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .text-center {
            text-align: center !important;
        }

        .login-header {
            color: #fff;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .login-wrapper {
            display: flex;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        .login-left {
            flex: 1;
            background-color: #0a192f;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .login-right {
            flex: 1;
            background-color: #0a192f;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .login-form-container {
            width: 100%;
            max-width: 400px;
            padding: 2rem;
            background: rgba(10, 25, 47, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(100, 255, 255, 0.1);
            z-index: 10;
        }

        .form-floating {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .form-floating input {
            height: 60px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 1.5rem 1rem 0.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating input:focus {
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 0 0.25rem rgba(255, 77, 0, 0.25);
            border-color: rgba(255, 77, 0, 0.5);
        }

        .form-floating label {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            padding: 1rem 1rem;
            pointer-events: none;
            border: 1px solid transparent;
            transform-origin: 0 0;
            transition: opacity .1s ease-in-out,transform .1s ease-in-out;
            color: rgba(255, 255, 255, 0.6);
        }

        .form-floating input:focus ~ label,
        .form-floating input:not(:placeholder-shown) ~ label {
            opacity: .65;
            transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
        }

        .form-floating input::placeholder {
            color: transparent;
        }

        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .form-check-input {
            width: 1.2em;
            height: 1.2em;
            margin-top: 0;
            margin-right: 0.5rem;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .form-check-input:checked {
            background-color: #0070f3;
            border-color: #0070f3;
        }

        .form-check-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .btn-login {
            background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 0.75rem;
            border-radius: 15px;
            width: 100%;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 112, 243, 0.4);
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(0, 112, 243, 0.6);
        }

        .btn-login:active {
            transform: translateY(-1px);
        }

        .login-links {
            display: flex;
            justify-content: space-between;
            margin-top: 1rem;
        }

        .login-links a {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .login-links a:hover {
            color: #00c6ff;
            text-shadow: 0 0 5px rgba(0, 198, 255, 0.3);
        }

        /* Input icons */
        .input-icon {
            position: absolute;
            top: 50%;
            {% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 1rem;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.4);
            font-size: 1.2rem;
            z-index: 10;
        }

        .form-floating input {
            padding-{% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 3rem;
        }

        /* Auto parts showcase */
        .parts-showcase {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            z-index: 1;
        }

        .part-item {
            position: absolute;
            width: 150px;
            height: 150px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.5));
            opacity: 0.8;
            transition: all 0.5s ease;
            transform: scale(0.8);
            animation: float-part 15s infinite ease-in-out;
        }

        .part-item:hover {
            transform: scale(1.1) !important;
            opacity: 1 !important;
            z-index: 10 !important;
        }

        @keyframes float-part {
            0% { transform: translateY(0) scale(0.8) rotate(0deg); }
            50% { transform: translateY(-20px) scale(0.85) rotate(5deg); }
            100% { transform: translateY(0) scale(0.8) rotate(0deg); }
        }

        .part-1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
            background-image: url('https://cdn.pixabay.com/photo/2013/07/12/14/53/car-148539_1280.png');
        }

        .part-2 {
            top: 60%;
            left: 20%;
            animation-delay: 2s;
            background-image: url('https://cdn.pixabay.com/photo/2014/04/02/10/39/disc-brake-304538_1280.png');
        }

        .part-3 {
            top: 30%;
            right: 15%;
            animation-delay: 1s;
            background-image: url('https://cdn.pixabay.com/photo/2013/07/13/10/21/car-157752_1280.png');
        }

        .part-4 {
            top: 70%;
            right: 10%;
            animation-delay: 3s;
            background-image: url('https://cdn.pixabay.com/photo/2012/04/13/20/37/gear-33848_1280.png');
        }

        .part-5 {
            top: 40%;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: 2.5s;
            background-image: url('https://cdn.pixabay.com/photo/2013/07/12/17/00/car-engine-151851_1280.png');
        }

        /* System title */
        .system-title-container {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 10;
        }

        .system-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: white;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        .system-title span {
            background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .system-subtitle {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.7);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Animated background */
        .animated-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a192f 0%, #112240 100%);
            z-index: 0;
            overflow: hidden;
        }

        .animated-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(0, 112, 243, 0.1) 0%, rgba(10, 25, 47, 0) 70%);
            z-index: 1;
        }

        .animated-line {
            position: absolute;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 198, 255, 0.5), transparent);
            z-index: 1;
            animation: scan-line 8s linear infinite;
        }

        @keyframes scan-line {
            0% { top: -5%; opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { top: 105%; opacity: 0; }
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 198, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 198, 255, 0.05) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: 1;
        }

        /* 3D car model */
        .car-model-container {
            position: relative;
            width: 100%;
            height: 100%;
            perspective: 1000px;
            z-index: 2;
        }

        .car-model {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotateY(0deg);
            width: 80%;
            height: 80%;
            background-image: url('https://cdn.pixabay.com/photo/2016/04/01/09/11/car-1299198_1280.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            animation: rotate-car 20s infinite linear;
        }

        @keyframes rotate-car {
            0% { transform: translate(-50%, -50%) rotateY(0deg); }
            100% { transform: translate(-50%, -50%) rotateY(360deg); }
        }

        /* Glow effects */
        .glow-effect {
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(0, 112, 243, 0.4) 0%, rgba(0, 198, 255, 0) 70%);
            filter: blur(20px);
            z-index: 0;
        }

        .glow-1 {
            top: 20%;
            left: 30%;
            animation: pulse-glow 4s infinite alternate;
        }

        .glow-2 {
            bottom: 20%;
            right: 30%;
            animation: pulse-glow 6s infinite alternate-reverse;
        }

        @keyframes pulse-glow {
            0% { opacity: 0.3; transform: scale(0.8); }
            100% { opacity: 0.7; transform: scale(1.2); }
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .login-wrapper {
                flex-direction: column;
            }
            
            .login-left, .login-right {
                flex: none;
                width: 100%;
                height: 50vh;
            }
            
            .login-form-container {
                max-width: 90%;
            }
            
            .system-title {
                font-size: 2rem;
            }
            
            .system-subtitle {
                font-size: 1rem;
            }
            
            .part-item {
                width: 100px;
                height: 100px;
            }
        }

        @media (max-width: 576px) {
            .login-left {
                height: 40vh;
            }
            
            .login-right {
                height: 60vh;
            }
            
            .system-title {
                font-size: 1.8rem;
            }
            
            .part-item {
                width: 80px;
                height: 80px;
            }
        }

        /* Floating icons */
        .floating-icon {
            position: absolute;
            font-size: 2rem;
            color: rgba(0, 198, 255, 0.7);
            animation: float-icon 6s infinite ease-in-out;
            z-index: 2;
        }

        .icon-1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .icon-2 {
            top: 70%;
            left: 15%;
            animation-delay: 1s;
        }

        .icon-3 {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .icon-4 {
            top: 60%;
            right: 15%;
            animation-delay: 3s;
        }

        @keyframes float-icon {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(10deg); }
            100% { transform: translateY(0) rotate(0deg); }
        }
    </style>
</head>

<body>
    <!-- Enhanced Language Switcher -->
    <div class="language-switcher">
        <div class="language-toggle-container">
            <a href="{% url 'set_language' %}?language=en&next={{ request.get_full_path }}"
               class="language-option {% if request.session.django_language == 'en' or not request.session.django_language %}active{% endif %}"
               onclick="addSwitchAnimation()">
                <span class="language-flag">🇺🇸</span>
                <span class="language-text">English</span>
            </a>
            <a href="{% url 'set_language' %}?language=ar&next={{ request.get_full_path }}"
               class="language-option {% if request.session.django_language == 'ar' %}active{% endif %}"
               onclick="addSwitchAnimation()">
                <span class="language-flag">🇸🇦</span>
                <span class="language-text">العربية</span>
            </a>
        </div>
    </div>

    <div class="login-wrapper">
        <!-- Left side with login form -->
        <div class="login-left">
            <!-- Animated background -->
            <div class="animated-bg">
                <div class="animated-line"></div>
                <div class="grid-overlay"></div>
                <div class="glow-effect glow-1"></div>
                <div class="glow-effect glow-2"></div>
            </div>
            
            <!-- Floating icons -->
            <div class="floating-icon icon-1"><i class="fas fa-cog"></i></div>
            <div class="floating-icon icon-2"><i class="fas fa-wrench"></i></div>
            
            <div class="system-title-container">
                <h1 class="system-title">{% custom_trans "Auto" %} <span>{% custom_trans "Parts" %}</span> {% custom_trans "Management" %}</h1>
                <p class="system-subtitle">{% custom_trans "Integrated platform for inventory, sales, and purchasing management for auto parts stores" %}</p>
            </div>
            
            <div class="login-form-container">
                {% if messages %}
                <div class="mb-4">
                    {% for message in messages %}
                    <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible fade show" role="alert" 
                         style="background: rgba({% if message.tags == 'error' %}220, 53, 69, 0.2{% elif message.tags == 'success' %}40, 167, 69, 0.2{% else %}255, 255, 255, 0.1{% endif %}); 
                                color: white; border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 15px;">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <h2 class="login-header text-center">{% custom_trans "Welcome Back!" %}</h2>
                
                <form class="user" method="post" action="{% url 'login_view' %}">
                    {% csrf_token %}
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" placeholder=" " required>
                        <label for="username">{% custom_trans "Username" %}</label>
                        <div class="input-icon">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder=" " required>
                        <label for="password">{% custom_trans "Password" %}</label>
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="customCheck">
                        <label class="form-check-label" for="customCheck">
                            {% custom_trans "Remember me" %}
                        </label>
                    </div>
                    <button type="submit" class="btn btn-login">
                        <i class="fas fa-sign-in-alt"></i> {% custom_trans "Login" %}
                    </button>
                    
                    <div class="login-links">
                        <a href="#"><i class="fas fa-key"></i> {% custom_trans "Forgot Password?" %}</a>
                        <a href="{% url 'register_view' %}"><i class="fas fa-user-plus"></i> {% custom_trans "Register" %}</a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Right side with auto parts showcase -->
        <div class="login-right">
            <!-- Animated background -->
            <div class="animated-bg">
                <div class="grid-overlay"></div>
                <div class="glow-effect glow-1"></div>
                <div class="glow-effect glow-2"></div>
            </div>
            
            <!-- Floating icons -->
            <div class="floating-icon icon-3"><i class="fas fa-oil-can"></i></div>
            <div class="floating-icon icon-4"><i class="fas fa-car-battery"></i></div>
            
            <!-- 3D car model -->
            <div class="car-model-container">
                <div class="car-model"></div>
            </div>
            
            <!-- Auto parts showcase -->
            <div class="parts-showcase">
                <div class="part-item part-1"></div>
                <div class="part-item part-2"></div>
                <div class="part-item part-3"></div>
                <div class="part-item part-4"></div>
                <div class="part-item part-5"></div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{% static 'sbadmin/vendor/jquery/jquery.min.js' %}"></script>
    <script src="{% static 'sbadmin/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{% static 'sbadmin/vendor/jquery-easing/jquery.easing.min.js' %}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{% static 'sbadmin/js/sb-admin-2.min.js' %}"></script>

    <!-- Animation Scripts -->
    <script>
        function addSwitchAnimation() {
            const container = document.querySelector('.language-toggle-container');
            container.classList.add('language-switch-animation');

            // Remove animation class after animation completes
            setTimeout(() => {
                container.classList.remove('language-switch-animation');
            }, 600);
        }

        // Add smooth transition when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const languageOptions = document.querySelectorAll('.language-option');

            languageOptions.forEach(option => {
                option.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                });

                option.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'scale(1)';
                    } else {
                        this.style.transform = 'scale(1.02)';
                    }
                });
            });

            // Add ripple effect on click
            languageOptions.forEach(option => {
                option.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
            
            // Create animated scan lines
            createScanLines();
            
            // Add hover effects to auto parts
            addPartsHoverEffects();
        });
        
        // Function to create animated scan lines
        function createScanLines() {
            const leftBg = document.querySelector('.login-left .animated-bg');
            const rightBg = document.querySelector('.login-right .animated-bg');
            
            // Create multiple scan lines with different speeds
            for (let i = 0; i < 3; i++) {
                const leftLine = document.createElement('div');
                leftLine.classList.add('animated-line');
                leftLine.style.animationDuration = `${8 + i * 4}s`;
                leftLine.style.animationDelay = `${i * 2}s`;
                leftLine.style.opacity = 0.3 - (i * 0.05);
                
                const rightLine = document.createElement('div');
                rightLine.classList.add('animated-line');
                rightLine.style.animationDuration = `${10 + i * 3}s`;
                rightLine.style.animationDelay = `${i * 1.5}s`;
                rightLine.style.opacity = 0.3 - (i * 0.05);
                
                leftBg.appendChild(leftLine);
                rightBg.appendChild(rightLine);
            }
        }
        
        // Function to add hover effects to auto parts
        function addPartsHoverEffects() {
            const parts = document.querySelectorAll('.part-item');
            
            parts.forEach(part => {
                part.addEventListener('mouseenter', function() {
                    // Add glow effect
                    const glow = document.createElement('div');
                    glow.classList.add('part-glow');
                    glow.style.position = 'absolute';
                    glow.style.top = '50%';
                    glow.style.left = '50%';
                    glow.style.transform = 'translate(-50%, -50%)';
                    glow.style.width = '200%';
                    glow.style.height = '200%';
                    glow.style.borderRadius = '50%';
                    glow.style.background = 'radial-gradient(circle, rgba(0, 112, 243, 0.4) 0%, rgba(0, 198, 255, 0) 70%)';
                    glow.style.filter = 'blur(15px)';
                    glow.style.zIndex = '-1';
                    
                    this.appendChild(glow);
                    
                    // Add info tooltip
                    const tooltip = document.createElement('div');
                    tooltip.classList.add('part-tooltip');
                    tooltip.style.position = 'absolute';
                    tooltip.style.bottom = '-60px';
                    tooltip.style.left = '50%';
                    tooltip.style.transform = 'translateX(-50%)';
                    tooltip.style.background = 'rgba(10, 25, 47, 0.9)';
                    tooltip.style.color = 'white';
                    tooltip.style.padding = '8px 15px';
                    tooltip.style.borderRadius = '10px';
                    tooltip.style.fontSize = '14px';
                    tooltip.style.whiteSpace = 'nowrap';
                    tooltip.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.3)';
                    tooltip.style.border = '1px solid rgba(0, 198, 255, 0.3)';
                    tooltip.style.zIndex = '100';
                    tooltip.style.opacity = '0';
                    tooltip.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    
                    // Set tooltip text based on part class
                    if (this.classList.contains('part-1')) {
                        tooltip.textContent = 'Car Body Parts';
                    } else if (this.classList.contains('part-2')) {
                        tooltip.textContent = 'Brake System Components';
                    } else if (this.classList.contains('part-3')) {
                        tooltip.textContent = 'Suspension Parts';
                    } else if (this.classList.contains('part-4')) {
                        tooltip.textContent = 'Transmission Components';
                    } else if (this.classList.contains('part-5')) {
                        tooltip.textContent = 'Engine Parts';
                    }
                    
                    this.appendChild(tooltip);
                    
                    // Show tooltip with animation
                    setTimeout(() => {
                        tooltip.style.opacity = '1';
                        tooltip.style.transform = 'translateX(-50%) translateY(-10px)';
                    }, 50);
                });
                
                part.addEventListener('mouseleave', function() {
                    // Remove glow effect
                    const glow = this.querySelector('.part-glow');
                    if (glow) {
                        glow.remove();
                    }
                    
                    // Remove tooltip with animation
                    const tooltip = this.querySelector('.part-tooltip');
                    if (tooltip) {
                        tooltip.style.opacity = '0';
                        tooltip.style.transform = 'translateX(-50%) translateY(0)';
                        
                        setTimeout(() => {
                            tooltip.remove();
                        }, 300);
                    }
                });
            });
        }
    </script>

    <style>
        /* Ripple effect styles */
        .language-option {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Enhanced hover effects */
        .language-toggle-container:hover .language-option:not(:hover) {
            opacity: 0.7;
        }

        .language-toggle-container:hover .language-option:hover {
            opacity: 1;
        }

        /* Pulse animation for active language */
        .language-option.active {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }
            50% {
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
            }
            100% {
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }
        }
    </style>
</body>

</html>
