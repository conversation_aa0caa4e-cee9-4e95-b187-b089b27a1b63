{% load static %}
{% load custom_i18n %}
<!DOCTYPE html>
<html lang="{% if request.session.django_language == 'ar' %}ar{% else %}en{% endif %}" dir="{% if request.session.django_language == 'ar' %}rtl{% else %}ltr{% endif %}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Auto Parts Management System - Two Factor Authentication">
    <meta name="author" content="">

    <title>{% custom_trans "Two-Factor Authentication" %} - {% custom_trans "Auto Parts Management System" %}</title>

    <!-- Custom fonts for this template-->
    <link href="{% static 'sbadmin/vendor/fontawesome-free/css/all.min.css' %}" rel="stylesheet" type="text/css">
    {% if request.session.django_language == 'ar' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;800;900&display=swap" rel="stylesheet">
    {% else %}
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">
    {% endif %}

    <!-- Custom styles for this template-->
    <link href="{% static 'sbadmin/css/sb-admin-2.min.css' %}" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            {% if request.session.django_language == 'ar' %}
            font-family: 'Cairo', sans-serif;
            {% else %}
            font-family: 'Nunito', sans-serif;
            {% endif %}
            min-height: 100vh;
        }

        .bg-login-image {
            background: url('{% static "service.jpg" %}');
            background-position: center;
            background-size: cover;
            position: relative;
        }

        .bg-login-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(102, 126, 234, 0.8);
        }

        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .card-body {
            padding: 2rem;
        }

        .form-control-user {
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            border: 2px solid #e3e6f0;
        }

        .form-control-user:focus {
            border-color: #5a5c69;
            box-shadow: 0 0 0 0.2rem rgba(90, 92, 105, 0.25);
        }

        .btn-user {
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            font-weight: 600;
        }

        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .language-toggle-container {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .language-option {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            color: #5a5c69;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .language-option.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .language-flag {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .verification-code-input {
            font-size: 1.5rem;
            text-align: center;
            letter-spacing: 0.3rem;
            font-weight: 600;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: 600;
            position: relative;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .step.completed {
            background: #1cc88a;
            color: white;
        }

        .step.inactive {
            background: #e3e6f0;
            color: #5a5c69;
        }

        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #e3e6f0;
            transform: translateY(-50%);
        }

        .step:last-child::after {
            display: none;
        }

        .step.completed::after {
            background: #1cc88a;
        }
    </style>
</head>

<body>
    <!-- Enhanced Language Switcher -->
    <div class="language-switcher">
        <div class="language-toggle-container">
            <a href="{% url 'set_language' %}?language=en&next={{ request.get_full_path }}"
               class="language-option {% if request.session.django_language == 'en' or not request.session.django_language %}active{% endif %}">
                <span class="language-flag">🇺🇸</span>
                <span class="language-text">English</span>
            </a>
            <a href="{% url 'set_language' %}?language=ar&next={{ request.get_full_path }}"
               class="language-option {% if request.session.django_language == 'ar' %}active{% endif %}">
                <span class="language-flag">🇸🇦</span>
                <span class="language-text">العربية</span>
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Outer Row -->
        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-12 col-md-9">
                <div class="card o-hidden border-0 shadow-lg my-5">
                    <div class="card-body p-0">
                        <!-- Nested Row within Card Body -->
                        <div class="row">
                            <div class="col-lg-6 d-none d-lg-block bg-login-image"></div>
                            <div class="col-lg-6">
                                <div class="p-5">
                                    <div class="text-center">
                                        <h1 class="h4 text-gray-900 mb-4">
                                            <i class="fas fa-shield-alt fa-fw mr-2"></i>
                                            {% custom_trans "Two-Factor Authentication" %}
                                        </h1>
                                    </div>

                                    <!-- Step Indicator -->
                                    <div class="step-indicator">
                                        {% if wizard.steps.current == 'auth' %}
                                            <div class="step completed">1</div>
                                            <div class="step active">2</div>
                                        {% else %}
                                            <div class="step active">1</div>
                                            <div class="step inactive">2</div>
                                        {% endif %}
                                    </div>

                                    {% if messages %}
                                        {% for message in messages %}
                                            <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible fade show" role="alert">
                                                {{ message }}
                                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                        {% endfor %}
                                    {% endif %}

                                    {% if wizard.steps.current == 'auth' %}
                                        <!-- Username/Password Step -->
                                        <form method="post" class="user">
                                            {% csrf_token %}
                                            {{ wizard.management_form }}
                                            
                                            <div class="form-group">
                                                <input type="text" class="form-control form-control-user"
                                                       name="{{ form.username.name }}" 
                                                       placeholder="{% custom_trans 'Enter Username...' %}"
                                                       value="{{ form.username.value|default:'' }}" required>
                                                {% if form.username.errors %}
                                                    <div class="text-danger mt-1">
                                                        {% for error in form.username.errors %}
                                                            <small>{{ error }}</small>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <div class="form-group">
                                                <input type="password" class="form-control form-control-user"
                                                       name="{{ form.password.name }}" 
                                                       placeholder="{% custom_trans 'Password' %}" required>
                                                {% if form.password.errors %}
                                                    <div class="text-danger mt-1">
                                                        {% for error in form.password.errors %}
                                                            <small>{{ error }}</small>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <button type="submit" class="btn btn-primary btn-user btn-block">
                                                <i class="fas fa-arrow-right fa-fw mr-2"></i>
                                                {% custom_trans "Continue" %}
                                            </button>
                                        </form>

                                    {% elif wizard.steps.current == 'token' %}
                                        <!-- Verification Code Step -->
                                        <div class="text-center mb-4">
                                            <p class="text-gray-600">
                                                {% custom_trans "Enter the 6-digit verification code from your authenticator app" %}
                                            </p>
                                        </div>

                                        <form method="post" class="user">
                                            {% csrf_token %}
                                            {{ wizard.management_form }}
                                            
                                            <div class="form-group">
                                                <input type="text" class="form-control form-control-user verification-code-input"
                                                       name="{{ form.otp_token.name }}" 
                                                       placeholder="000000"
                                                       maxlength="6" 
                                                       pattern="[0-9]{6}"
                                                       autocomplete="off" required>
                                                {% if form.otp_token.errors %}
                                                    <div class="text-danger mt-1">
                                                        {% for error in form.otp_token.errors %}
                                                            <small>{{ error }}</small>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <button type="submit" class="btn btn-success btn-user btn-block">
                                                <i class="fas fa-sign-in-alt fa-fw mr-2"></i>
                                                {% custom_trans "Verify & Login" %}
                                            </button>
                                        </form>

                                        <hr>
                                        <div class="text-center">
                                            <a class="small" href="{% url 'two_factor:backup_tokens' %}">
                                                {% custom_trans "Use backup token instead" %}
                                            </a>
                                        </div>

                                    {% elif wizard.steps.current == 'backup' %}
                                        <!-- Backup Token Step -->
                                        <div class="text-center mb-4">
                                            <p class="text-gray-600">
                                                {% custom_trans "Enter one of your backup tokens" %}
                                            </p>
                                        </div>

                                        <form method="post" class="user">
                                            {% csrf_token %}
                                            {{ wizard.management_form }}
                                            
                                            <div class="form-group">
                                                <input type="text" class="form-control form-control-user"
                                                       name="{{ form.otp_token.name }}" 
                                                       placeholder="{% custom_trans 'Backup Token' %}"
                                                       autocomplete="off" required>
                                                {% if form.otp_token.errors %}
                                                    <div class="text-danger mt-1">
                                                        {% for error in form.otp_token.errors %}
                                                            <small>{{ error }}</small>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <button type="submit" class="btn btn-warning btn-user btn-block">
                                                <i class="fas fa-key fa-fw mr-2"></i>
                                                {% custom_trans "Use Backup Token" %}
                                            </button>
                                        </form>
                                    {% endif %}

                                    <hr>
                                    <div class="text-center">
                                        <a class="small" href="{% url 'login_view' %}">
                                            <i class="fas fa-arrow-left fa-fw mr-1"></i>
                                            {% custom_trans "Back to Regular Login" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{% static 'sbladmin/vendor/jquery/jquery.min.js' %}"></script>
    <script src="{% static 'sbladmin/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{% static 'sbladmin/vendor/jquery-easing/jquery.easing.min.js' %}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{% static 'sbladmin/js/sb-admin-2.min.js' %}"></script>

    <script>
        // Auto-focus on verification code input
        document.addEventListener('DOMContentLoaded', function() {
            const codeInput = document.querySelector('.verification-code-input');
            if (codeInput) {
                codeInput.focus();
                
                // Auto-submit when 6 digits are entered
                codeInput.addEventListener('input', function() {
                    if (this.value.length === 6) {
                        // Small delay to ensure user sees the complete code
                        setTimeout(() => {
                            this.closest('form').submit();
                        }, 500);
                    }
                });
            }
        });

        function addSwitchAnimation() {
            document.querySelector('.language-toggle-container').style.transform = 'scale(0.95)';
            setTimeout(() => {
                document.querySelector('.language-toggle-container').style.transform = 'scale(1)';
            }, 150);
        }
    </script>
</body>

</html>
