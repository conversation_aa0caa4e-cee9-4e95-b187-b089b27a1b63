{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}
{% load custom_filters %}

{% block title %}{% custom_trans "Expenses" %} - Auto Parts Management{% endblock %}

{% block extra_css %}
<!-- Custom styles for this page -->
<link href="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.css' %}" rel="stylesheet">

<style>
    /* Enhanced page styling matching other pages */
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1.5rem !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .page-heading::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 112, 243, 0.05), transparent);
        animation: page-shine 4s infinite;
    }

    @keyframes page-shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .btn-add-expense {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.75rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        color: white !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.3) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .btn-add-expense::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-add-expense:hover::before {
        left: 100%;
    }

    .btn-add-expense:hover {
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 10px 30px rgba(0, 112, 243, 0.5) !important;
        color: white !important;
    }

    /* Enhanced table styling */
    .table-container {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        overflow: hidden !important;
    }

    .filter-container {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        margin-bottom: 2rem !important;
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-radius: 0.5rem !important;
    }

    /* Modal enhancements */
    .modal {
        z-index: 9999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: auto !important;
        background-color: rgba(10, 25, 47, 0.8) !important;
    }

    .modal-backdrop {
        display: none !important;
    }

    .modal-dialog {
        z-index: 10000 !important;
        position: relative !important;
        margin: 50px auto !important;
        pointer-events: auto !important;
    }

    .modal-content {
        z-index: 10001 !important;
        position: relative !important;
        pointer-events: auto !important;
        margin: 0 auto !important;
    }

    .modal input,
    .modal textarea,
    .modal select,
    .modal button {
        pointer-events: auto !important;
        z-index: 1060 !important;
        position: relative !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <div class="d-sm-flex align-items-center justify-content-between">
        <div>
            <h1 class="h3 mb-0" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important;">
                <i class="fas fa-receipt fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important; animation: pulse-icon 2s infinite ease-in-out;"></i>
                {% custom_trans "Expenses" %}
            </h1>
            <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7); font-size: 1rem;">
                {% custom_trans "Track and manage your business expenses here" %}
            </p>
        </div>
        <button type="button" class="btn btn-add-expense" onclick="openAddModal()">
            <i class="fas fa-plus-circle fa-sm fa-fw mr-2"></i>{% custom_trans "Add Expense" %}
        </button>
    </div>
</div>

<!-- Filter Form -->
<div class="card filter-container shadow mb-4">
    <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(0, 112, 243, 0.2); box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1);">
        <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            <i class="fas fa-filter fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: pulse-icon 2s infinite ease-in-out;"></i>
            {% custom_trans "Filter Expenses" %}
        </h6>
    </div>
    <div class="card-body">
        <form action="{% url 'expense_view' %}" method="get">
            <div class="form-row">
                <div class="form-group col-md-4">
                    <label for="provider" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-truck fa-fw mr-1"></i>{% custom_trans "Provider" %}
                    </label>
                    <select class="form-control" id="provider" name="provider"
                            style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        <option value="">{% custom_trans "All Providers" %}</option>
                        {% for provider in providers %}
                        <option value="{{ provider.id }}" {% if selected_provider == provider.id|stringformat:"s" %}selected{% endif %}>{{ provider.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group col-md-4">
                    <label for="product" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-box fa-fw mr-1"></i>{% custom_trans "Product" %}
                    </label>
                    <select class="form-control" id="product" name="product"
                            style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        <option value="">{% custom_trans "All Products" %}</option>
                        {% for product in products %}
                        <option value="{{ product.id }}" {% if selected_product == product.id|stringformat:"s" %}selected{% endif %}>{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group col-md-4">
                    <label for="date" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-calendar-alt fa-fw mr-1"></i>{% custom_trans "Date" %}
                    </label>
                    <input type="date" class="form-control" id="date" name="date" value="{{ selected_date }}"
                           style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                </div>
            </div>
            <div class="text-center">
                <button type="submit" class="btn btn-primary mr-3"
                        style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border: none; border-radius: 0.5rem; font-weight: 600; padding: 0.5rem 1.5rem;">
                    <i class="fas fa-search fa-fw mr-1"></i>{% custom_trans "Filter" %}
                </button>
                <a href="{% url 'expense_view' %}" class="btn btn-secondary"
                   style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white; padding: 0.5rem 1.5rem;">
                    <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Reset" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Expenses Table -->
<div class="card table-container shadow mb-4">
    <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(0, 112, 243, 0.2); box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1);">
        <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            <i class="fas fa-list fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: pulse-icon 2s infinite ease-in-out;"></i>
            {% custom_trans "Expenses List" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-hashtag fa-fw mr-1"></i>{% custom_trans "ID" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-truck fa-fw mr-1"></i>{% custom_trans "Provider" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-box fa-fw mr-1"></i>{% custom_trans "Product" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-sort-numeric-up fa-fw mr-1"></i>{% custom_trans "Quantity" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-dollar-sign fa-fw mr-1"></i>{% custom_trans "Price" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-calculator fa-fw mr-1"></i>{% custom_trans "Total" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-calendar fa-fw mr-1"></i>{% custom_trans "Date" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-cogs fa-fw mr-1"></i>{% custom_trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "ID" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Provider" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Product" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Quantity" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Price" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Total" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Date" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Actions" %}</th>
                    </tr>
                </tfoot>
                <tbody>
                    {% for expense in expenses %}
                    <tr style="color: rgba(255, 255, 255, 0.8);">
                        <td>{{ expense.id }}</td>
                        <td>
                            <i class="fas fa-truck fa-fw mr-2" style="color: #0070f3;"></i>
                            {{ expense.provider.name }}
                        </td>
                        <td>
                            <i class="fas fa-cube fa-fw mr-2" style="color: #00c6ff;"></i>
                            {{ expense.product.name }}
                        </td>
                        <td>
                            <i class="fas fa-sort-numeric-up fa-fw mr-2" style="color: #28a745;"></i>
                            {{ expense.quantity }}
                        </td>
                        <td>
                            <i class="fas fa-dollar-sign fa-fw mr-2" style="color: #ffc107;"></i>
                            ${{ expense.price }}
                        </td>
                        <td>
                            <i class="fas fa-calculator fa-fw mr-2" style="color: #17a2b8;"></i>
                            ${{ expense.price|multiply:expense.quantity }}
                        </td>
                        <td>
                            <i class="fas fa-calendar-alt fa-fw mr-2" style="color: #6f42c1;"></i>
                            {{ expense.date|date:"M d, Y" }}
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm edit-expense"
                                data-id="{{ expense.id }}"
                                data-provider="{{ expense.provider.id }}"
                                data-product="{{ expense.product.id }}"
                                data-quantity="{{ expense.quantity }}"
                                data-price="{{ expense.price }}"
                                style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; border-radius: 0.5rem; color: white; margin-right: 0.5rem;">
                                <i class="fas fa-edit fa-fw"></i> {% custom_trans "Edit" %}
                            </button>
                            <button type="button" class="btn btn-sm delete-expense" data-id="{{ expense.id }}"
                                    style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); border: none; border-radius: 0.5rem; color: white;">
                                <i class="fas fa-trash fa-fw"></i> {% custom_trans "Delete" %}
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Expense Modal -->
<div class="modal fade" id="editExpenseModal" tabindex="-1" role="dialog" aria-labelledby="editExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editExpenseModalLabel">Edit Expense</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <form action="{% url 'expense_edit' %}" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <input type="hidden" name="expense_id" id="edit-expense-id">
                    <div class="form-group">
                        <label for="edit-expense-provider">Provider</label>
                        <select class="form-control" id="edit-expense-provider" name="provider" required>
                            {% for provider in providers %}
                            <option value="{{ provider.id }}">{{ provider.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit-expense-product">Product</label>
                        <select class="form-control" id="edit-expense-product" name="product" required>
                            {% for product in products %}
                            <option value="{{ product.id }}">{{ product.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit-expense-quantity">Quantity</label>
                        <input type="number" class="form-control" id="edit-expense-quantity" name="quantity" min="1" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-expense-price">Price</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" step="0.01" min="0.01" max="{{ max_price }}" class="form-control" id="edit-expense-price" name="price" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Expense Modal -->
<div class="modal fade" id="addExpenseModal" tabindex="-1" role="dialog" aria-labelledby="addExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addExpenseModalLabel">Add Expense</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <form action="{% url 'expense_add' %}" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group">
                        <label for="add-expense-provider">Provider</label>
                        <select class="form-control" id="add-expense-provider" name="provider" required>
                            <option value="">Select Provider</option>
                            {% for provider in providers %}
                            <option value="{{ provider.id }}">{{ provider.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="add-expense-product">Product</label>
                        <select class="form-control" id="add-expense-product" name="product" required>
                            <option value="">Select Product</option>
                            {% for product in products %}
                            <option value="{{ product.id }}">{{ product.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="add-expense-quantity">Quantity</label>
                        <input type="number" class="form-control" id="add-expense-quantity" name="quantity" min="1" required>
                    </div>
                    <div class="form-group">
                        <label for="add-expense-price">Price per Unit</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" step="0.01" min="0.01" max="{{ max_price }}" class="form-control" id="add-expense-price" name="price" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="add-expense-total">Total</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text" class="form-control" id="add-expense-total" readonly>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Expense</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Expense Modal -->
<div class="modal fade" id="deleteExpenseModal" tabindex="-1" role="dialog" aria-labelledby="deleteExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteExpenseModalLabel">Delete Expense</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">Are you sure you want to delete this expense? This action cannot be undone.</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                <form action="{% url 'expense_remove' %}" method="post">
                    {% csrf_token %}
                    <input type="hidden" name="expense_id" id="delete-expense-id">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Page level plugins -->
<script src="{% static 'sbadmin/vendor/datatables/jquery.dataTables.min.js' %}"></script>
<script src="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.js' %}"></script>

<!-- Page level custom scripts -->
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#dataTable').DataTable({
            "order": [[0, "desc"]] // Sort by ID in descending order by default
        });

        // Edit Expense
        $('.edit-expense').click(function() {
            var id = $(this).data('id');
            var provider = $(this).data('provider');
            var product = $(this).data('product');
            var quantity = $(this).data('quantity');
            var price = $(this).data('price');

            $('#edit-expense-id').val(id);
            $('#edit-expense-provider').val(provider);
            $('#edit-expense-product').val(product);
            $('#edit-expense-quantity').val(quantity);
            $('#edit-expense-price').val(price);

            $('#editExpenseModal').modal('show');
        });

        // Delete Expense
        $('.delete-expense').click(function() {
            var id = $(this).data('id');
            $('#delete-expense-id').val(id);
            $('#deleteExpenseModal').modal('show');
        });

        // Add Expense - Calculate total when quantity or price changes
        function calculateAddTotal() {
            var quantity = parseInt($('#add-expense-quantity').val()) || 0;
            var price = parseFloat($('#add-expense-price').val()) || 0;
            var total = quantity * price;
            $('#add-expense-total').val(total.toFixed(2));
        }

        $('#add-expense-quantity, #add-expense-price').on('input', calculateAddTotal);

        // Force remove any existing backdrops on page load
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });

    // Custom modal functions
    function openAddModal() {
        // Remove any existing backdrop
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Show modal with custom method
        $('#addExpenseModal').css({
            'display': 'block',
            'z-index': '9999',
            'position': 'fixed',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(10, 25, 47, 0.8)',
            'overflow': 'auto'
        }).addClass('show');

        // Ensure all elements are interactive
        $('#addExpenseModal input, #addExpenseModal select, #addExpenseModal button').css({
            'pointer-events': 'auto',
            'z-index': '10001'
        });

        // Focus first input
        setTimeout(function() {
            $('#add-expense-provider').focus();
        }, 100);
    }

    function closeAddModal() {
        $('#addExpenseModal').css('display', 'none').removeClass('show');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    }

    // Fix modal interaction issues
    $('#addExpenseModal').on('click', function(e) {
        if (e.target === this) {
            closeAddModal();
        }
    });
</script>
{% endblock %}
