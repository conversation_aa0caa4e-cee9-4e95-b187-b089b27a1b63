from django.shortcuts import render, redirect
from django.contrib.auth import logout, authenticate, login
from . import models
from django.contrib import messages
from . import forms
from decimal import Decimal, InvalidOperation
from .translation import get_translation


def dashboard_view(request):
    if request.user.is_authenticated:
        # Get counts for dashboard stats
        products_count = models.Product.objects.count()
        providers_count = models.Provider.objects.count()
        purchasers_count = models.Purchaser.objects.count()
        purchases_count = models.Purchases.objects.count()
        expenses_count = models.Expense.objects.count()

        # Calculate total expenses amount
        total_expenses = Decimal('0.00')
        for expense in models.Expense.objects.all():
            try:
                expense_amount = Decimal(expense.price) * expense.quantity
                total_expenses += expense_amount
            except (InvalidOperation, TypeError):
                # Skip invalid values
                pass

        # Calculate total purchaser amounts
        total_purchaser_amounts = Decimal('0.00')
        for purchaser in models.Purchaser.objects.all():
            try:
                total_purchaser_amounts += Decimal(purchaser.amount)
            except (InvalidOperation, TypeError):
                # Skip invalid values
                pass

        # Calculate the difference
        difference = total_purchaser_amounts - total_expenses

        # Calculate percentages for the chart
        if total_expenses > Decimal('0.00') or total_purchaser_amounts > Decimal('0.00'):
            total_sum = total_expenses + total_purchaser_amounts
            expenses_percentage = (total_expenses / total_sum * 100).quantize(Decimal('0.01'))
            purchaser_percentage = (total_purchaser_amounts / total_sum * 100).quantize(Decimal('0.01'))
        else:
            expenses_percentage = Decimal('0.00')
            purchaser_percentage = Decimal('0.00')

        # Format currency values
        formatted_total_expenses = "${:,.2f}".format(total_expenses)
        formatted_total_purchaser_amounts = "${:,.2f}".format(total_purchaser_amounts)
        formatted_difference = "${:,.2f}".format(difference)

        context = {
            'products_count': products_count,
            'providers_count': providers_count,
            'purchasers_count': purchasers_count,
            'purchases_count': purchases_count,
            'expenses_count': expenses_count,
            'total_expenses': total_expenses,
            'total_purchaser_amounts': total_purchaser_amounts,
            'difference': difference,
            'formatted_total_expenses': formatted_total_expenses,
            'formatted_total_purchaser_amounts': formatted_total_purchaser_amounts,
            'formatted_difference': formatted_difference,
            'expenses_percentage': expenses_percentage,
            'purchaser_percentage': purchaser_percentage
        }
        return render(request, 'dashboard.html', context=context)
    return redirect('login_view')

def product_view(request):
    if request.user.is_authenticated:
        products = models.Product.objects.all()
        # Get stock quantities for each product
        for product in products:
            try:
                stock = models.Stock.objects.get(product=product)
                product.stock_quantity = stock.quantity_total
            except models.Stock.DoesNotExist:
                product.stock_quantity = 0

        context = {'products': products}
        return render(request, 'product.html', context=context)
    return redirect('login_view')

def product_add_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            # Get all required fields
            name = request.POST.get('name')
            reference = request.POST.get('reference')
            brand = request.POST.get('brand')
            model = request.POST.get('model')
            description = request.POST.get('description')
            price = request.POST.get('price')
            bulk_price = request.POST.get('bulk_price')
            quantity = request.POST.get('quantity', 0)

            # Validate all fields are provided
            if not all([name, reference, brand, model, description, price, bulk_price]):
                messages.error(request, 'All fields are required')
                return render(request, 'add_product.html')

            # Check if product already exists
            try:
                existing_product = models.Product.objects.filter(reference=reference).first()
                if existing_product:
                    messages.error(request, f'Product with reference {reference} already exists')
                    return render(request, 'add_product.html')

                # Validate data types
                try:
                    price = float(price)
                    bulk_price = float(bulk_price)
                    quantity = int(quantity)
                except ValueError:
                    messages.error(request, 'Invalid price or quantity values')
                    return render(request, 'add_product.html')

                # Create and save the product
                product = models.Product(
                    name=name,
                    reference=reference,
                    brand=brand,
                    model=model,
                    description=description,
                    price=price,
                    bulk_price=bulk_price
                )
                product.save()

                # Create stock entry if quantity provided
                if quantity > 0:
                    stock = models.Stock(product=product, quantity_total=quantity)
                    stock.save()

                messages.success(request, 'The Product added Successfully')
                return redirect('product_view')
            except Exception as e:
                messages.error(request, f'Error adding product: {str(e)}')
                return render(request, 'add_product.html')

        # GET request - show the form
        return render(request, 'add_product.html')
    return redirect('login_view')
def product_edit_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('id')

            # Check if the product exists
            try:
                product = models.Product.objects.get(id=id)

                # Get form data
                name = request.POST.get('name')
                reference = request.POST.get('reference')
                brand = request.POST.get('brand')
                model = request.POST.get('model')
                description = request.POST.get('description')
                price = request.POST.get('price')
                bulk_price = request.POST.get('bulk_price')

                # Validate all fields are provided
                if not all([name, reference, brand, model, description, price, bulk_price]):
                    messages.error(request, 'All fields are required')
                    return redirect('product_view')

                # Validate data types
                try:
                    price = float(price)
                    bulk_price = float(bulk_price)
                except ValueError:
                    messages.error(request, 'Invalid price values')
                    return redirect('product_view')

                # Update product
                product.name = name
                product.reference = reference
                product.brand = brand
                product.model = model
                product.description = description
                product.price = price
                product.bulk_price = bulk_price
                product.save()

                messages.success(request, 'The Product Edited Successfully')
                return redirect('product_view')
            except models.Product.DoesNotExist:
                messages.error(request, 'Product not found')
                return redirect('product_view')
            except Exception as e:
                messages.error(request, f'Error editing product: {str(e)}')
                return redirect('product_view')
        return redirect('product_view')
    return redirect('login_view')





def product_remove_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('id')

            # Check if the product exists
            try:
                product = models.Product.objects.get(id=id)

                # Check if there are related stock entries
                try:
                    stock = models.Stock.objects.get(product=product)
                    stock.delete()
                except models.Stock.DoesNotExist:
                    pass

                # Delete the product
                product.delete()
                messages.success(request, 'The Product is deleted successfully')
            except models.Product.DoesNotExist:
                messages.error(request, 'Product not found')
            except Exception as e:
                messages.error(request, f'Error deleting product: {str(e)}')

            return redirect('product_view')
        return redirect('product_view')
    return redirect('login_view')


def provider_view(request):
    if request.user.is_authenticated:
        providers = models.Provider.objects.all()
        context = {'providers': providers}
        return render(request, 'provider.html', context=context)
    return redirect('login_view')


def provider_add_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            name = request.POST.get('name')
            phone = request.POST.get('phone')  # Fixed: was getting 'name' twice

            # Validate fields
            if not all([name, phone]):
                messages.error(request, 'All fields are required')
                return render(request, 'provider_add.html')

            # Check if provider already exists
            try:
                existing_provider = models.Provider.objects.filter(name=name, phone=phone).first()
                if existing_provider:
                    messages.error(request, f'Provider with name {name} and phone {phone} already exists')
                    return render(request, 'provider_add.html')

                # Create and save provider
                provider = models.Provider(name=name, phone=phone)
                provider.save()
                messages.success(request, 'The Provider Added Successfully')
                return redirect('provider_view')
            except Exception as e:
                messages.error(request, f'Error adding provider: {str(e)}')
                return render(request, 'provider_add.html')

        return render(request, 'provider_add.html')
    return redirect('login_view')

def provider_edit_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('id')

            # Check if provider exists
            try:
                provider = models.Provider.objects.get(id=id)

                # Get form data
                name = request.POST.get('name')
                phone = request.POST.get('phone')

                # Validate fields
                if not all([name, phone]):
                    messages.error(request, 'All fields are required')
                    return redirect('provider_view')

                # Update provider
                provider.name = name
                provider.phone = phone
                provider.save()
                messages.success(request, 'The Provider Edited Successfully')
            except models.Provider.DoesNotExist:
                messages.error(request, 'Provider not found')
            except Exception as e:
                messages.error(request, f'Error editing provider: {str(e)}')

            return redirect('provider_view')
        return redirect('provider_view')
    return redirect('login_view')
def provider_remove_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('id')

            # Check if provider exists
            try:
                provider = models.Provider.objects.get(id=id)

                # Check if there are related expenses
                expenses = models.Expense.objects.filter(provider=provider).exists()
                if expenses:
                    messages.error(request, 'Cannot delete provider with related expenses')
                    return redirect('provider_view')

                # Delete provider
                provider.delete()
                messages.success(request, 'The provider is deleted successfully')
            except models.Provider.DoesNotExist:
                messages.error(request, 'Provider not found')
            except Exception as e:
                messages.error(request, f'Error deleting provider: {str(e)}')

            return redirect('provider_view')
        return redirect('provider_view')
    return redirect('login_view')


def purchaser_view(request):
    if request.user.is_authenticated:
        purchasers = models.Purchaser.objects.all()
        context = {'purchasers': purchasers}
        return render(request, 'purchaser.html', context=context)
    return redirect('login_view')

def purchaser_add_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            name = request.POST.get('name')
            phone = request.POST.get('phone')
            special_client = request.POST.get('check_if') == 'on'

            # Validate fields
            if not all([name, phone]):
                messages.error(request, 'Name and phone are required')
                return render(request, 'purchaser_add.html')

            # Check if purchaser already exists
            try:
                existing_purchaser = models.Purchaser.objects.filter(name=name, phone=phone).first()
                if existing_purchaser:
                    messages.error(request, f'Purchaser with name {name} and phone {phone} already exists')
                    return render(request, 'purchaser_add.html')

                # Create and save purchaser with initial amount of 0
                purchaser = models.Purchaser(name=name, phone=phone, special_client=special_client, amount=0)
                purchaser.save()
                messages.success(request, 'The purchaser added successfully')
                return redirect('purchaser_view')
            except Exception as e:
                messages.error(request, f'Error adding purchaser: {str(e)}')
                return render(request, 'purchaser_add.html')

        return render(request, 'purchaser_add.html')
    return redirect('login_view')

def purchaser_edit_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('id')

            # Check if purchaser exists
            try:
                purchaser = models.Purchaser.objects.get(id=id)

                # Get form data
                name = request.POST.get('name')
                phone = request.POST.get('phone')
                amount = request.POST.get('amount')
                special_client = request.POST.get('special_client') == 'on'

                # Validate fields
                if not all([name, phone, amount]):
                    messages.error(request, 'Name, phone, and amount are required')
                    return redirect('purchaser_view')

                # Validate amount is a valid decimal
                try:
                    amount = float(amount)
                except ValueError:
                    messages.error(request, 'Amount must be a valid number')
                    return redirect('purchaser_view')

                # Update purchaser
                purchaser.name = name
                purchaser.phone = phone
                purchaser.amount = amount
                purchaser.special_client = special_client
                purchaser.save()
                messages.success(request, 'The purchaser edited successfully')
            except models.Purchaser.DoesNotExist:
                messages.error(request, 'Purchaser not found')
            except Exception as e:
                messages.error(request, f'Error editing purchaser: {str(e)}')

            return redirect('purchaser_view')
        return redirect('purchaser_view')
    return redirect('login_view')

def purchaser_remove_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('id')

            # Check if purchaser exists
            try:
                purchaser = models.Purchaser.objects.get(id=id)

                # Check if there are related purchases
                purchases = models.Purchases.objects.filter(purchaser=purchaser).exists()
                if purchases:
                    messages.error(request, 'Cannot delete purchaser with related purchases')
                    return redirect('purchaser_view')

                # Delete purchaser
                purchaser.delete()
                messages.success(request, 'The purchaser is deleted successfully')
            except models.Purchaser.DoesNotExist:
                messages.error(request, 'Purchaser not found')
            except Exception as e:
                messages.error(request, f'Error deleting purchaser: {str(e)}')

            return redirect('purchaser_view')
        return redirect('purchaser_view')
    return redirect('login_view')


def purchases_view(request):
    if request.user.is_authenticated:
        # Get all purchasers and products for filter dropdowns
        purchasers = models.Purchaser.objects.all()
        products = models.Product.objects.all()

        # Start with all purchases
        purchases_query = models.Purchases.objects.all()

        # Check if any filter parameters are provided (works for both GET and POST)
        filter_params = request.GET if request.method == 'GET' else request.POST

        purchaser_id = filter_params.get('purchaser')
        product_id = filter_params.get('product')
        date_str = filter_params.get('date')

        # Track if any filters were applied
        filters_applied = False

        # Apply filters if provided
        if purchaser_id:
            purchases_query = purchases_query.filter(purchaser_id=purchaser_id)
            filters_applied = True

        if product_id:
            purchases_query = purchases_query.filter(product_id=product_id)
            filters_applied = True

        if date_str and date_str.strip():
            # Convert date string to date object
            try:
                from datetime import datetime
                date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                purchases_query = purchases_query.filter(date__date=date_obj)
                filters_applied = True
            except (ValueError, TypeError):
                messages.error(request, 'Invalid date format')

        # Add a message if filters were applied
        if filters_applied:
            filter_description = []
            if purchaser_id:
                try:
                    purchaser = models.Purchaser.objects.get(id=purchaser_id)
                    filter_description.append(f"Purchaser: {purchaser.name}")
                except:
                    pass
            if product_id:
                try:
                    product = models.Product.objects.get(id=product_id)
                    filter_description.append(f"Product: {product.name}")
                except:
                    pass
            if date_str and date_str.strip():
                filter_description.append(f"Date: {date_str}")

            if filter_description:
                messages.info(request, f"Showing filtered results for {', '.join(filter_description)}")

        context = {
            'purchases': purchases_query,
            'purchasers': purchasers,
            'products': products,
            # Pre-select the filter values in the form
            'selected_purchaser': purchaser_id,
            'selected_product': product_id,
            'selected_date': date_str
        }
        return render(request, 'purchases.html', context=context)
    return redirect('login_view')
def purchases_add_view(request):
    if request.user.is_authenticated:
        # Get all purchasers and products for dropdowns
        purchasers = models.Purchaser.objects.all()
        products = models.Product.objects.all()

        # Calculate max amount value from model definition
        max_digits = models.Purchaser._meta.get_field('amount').max_digits
        decimal_places = models.Purchaser._meta.get_field('amount').decimal_places
        max_amount = Decimal('9' * (max_digits - decimal_places) + '.' + '9' * decimal_places)

        context = {
            'purchasers': purchasers,
            'products': products,
            'max_amount': max_amount
        }

        if request.method == 'POST':
            purchaser_id = request.POST.get('purchaser')
            product_id = request.POST.get('product')
            quantity = request.POST.get('quantity')

            # Debug the checkbox values
            debt_value = request.POST.get('debt')
            bulk_value = request.POST.get('bulk')

            # Handle checkbox values more robustly
            debt_check = debt_value == 'on' or debt_value == 'true' or debt_value == '1'
            bulk_check = bulk_value == 'on' or bulk_value == 'true' or bulk_value == '1'

            # Add debug messages
            print(f"Debt checkbox value: {debt_value}, Interpreted as: {debt_check}")
            print(f"Bulk checkbox value: {bulk_value}, Interpreted as: {bulk_check}")

            # Validate required fields
            if not all([purchaser_id, product_id, quantity]):
                messages.error(request, 'Purchaser, product, and quantity are required')
                return render(request, 'purchases_add.html', context=context)

            try:
                # Convert quantity to integer
                quantity = int(quantity)
                if quantity <= 0:
                    messages.error(request, 'Quantity must be greater than zero')
                    return render(request, 'purchases_add.html', context=context)

                # Check if purchaser and product exist
                try:
                    purchaser = models.Purchaser.objects.get(id=purchaser_id)
                    product = models.Product.objects.get(id=product_id)

                    # Check if there's enough stock
                    try:
                        stock = models.Stock.objects.get(product=product)
                        if stock.quantity_total < quantity:
                            messages.error(request, f'Not enough stock. Available: {stock.quantity_total}')
                            return render(request, 'purchases_add.html', context=context)
                    except models.Stock.DoesNotExist:
                        messages.error(request, 'Product is not in stock')
                        return render(request, 'purchases_add.html', context=context)

                    # Create purchase
                    purchase = models.Purchases(
                        purchaser=purchaser,
                        product=product,
                        quantity=quantity,
                        debt=debt_check,
                        bulk=bulk_check
                    )
                    purchase.save()

                    # Update stock
                    stock.quantity_total -= quantity
                    stock.save()

                    # Always update purchaser's amount, regardless of debt checkbox
                    try:
                        # Get price based on bulk or regular
                        price = Decimal(product.bulk_price) if bulk_check else Decimal(product.price)

                        # Calculate total price
                        total_price = price * quantity

                        # Debug the amount calculation
                        print(f"Current purchaser amount: {purchaser.amount}")
                        print(f"Total price to add: {total_price}")

                        # Calculate new amount
                        new_amount = purchaser.amount + total_price
                        print(f"New amount calculated: {new_amount}")

                        # Check if new amount exceeds the maximum allowed by the model
                        max_digits = models.Purchaser._meta.get_field('amount').max_digits
                        decimal_places = models.Purchaser._meta.get_field('amount').decimal_places
                        max_value = Decimal('9' * (max_digits - decimal_places) + '.' + '9' * decimal_places)

                        if new_amount > max_value:
                            messages.error(request, f'Amount would exceed maximum allowed amount of {max_value}. Current amount: {purchaser.amount}, Purchase total: {total_price}')
                            return render(request, 'purchases_add.html', context=context)

                        # Update purchaser amount
                        purchaser.amount = new_amount
                        purchaser.save()

                        # Verify the update
                        updated_purchaser = models.Purchaser.objects.get(id=purchaser.id)
                        print(f"Purchaser amount after save: {updated_purchaser.amount}")

                        # Add a success message about the amount update
                        if debt_check:
                            messages.success(request, f'Debt of ${total_price} added to {purchaser.name}. New total: ${new_amount}')
                        else:
                            messages.success(request, f'Amount of ${total_price} added to {purchaser.name}. New total: ${new_amount}')
                    except InvalidOperation as e:
                        messages.error(request, f'Error processing price: {str(e)}. Please check the price values.')
                        return render(request, 'purchases_add.html', context=context)

                    messages.success(request, 'The purchase is added successfully')
                    return redirect('purchases_view')
                except models.Purchaser.DoesNotExist:
                    messages.error(request, 'Purchaser not found')
                except models.Product.DoesNotExist:
                    messages.error(request, 'Product not found')
            except ValueError:
                messages.error(request, 'Invalid quantity value')
            except InvalidOperation as e:
                messages.error(request, f'Error processing decimal values: {str(e)}. Please check the price values.')
            except Exception as e:
                messages.error(request, f'Error adding purchase: {str(e)}')

            return render(request, 'purchases_add.html', context=context)

        # GET request - show form
        return render(request, 'purchases_add.html', context=context)
    return redirect('login_view')
def purchases_edit_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('purchase_id')

            # Debug the ID
            print(f"Edit - Purchase ID: {id}")

            # Check if purchase exists
            try:
                purchase = models.Purchases.objects.get(id=id)

                # Get original values for comparison
                original_quantity = purchase.quantity
                original_product = purchase.product
                original_debt = purchase.debt
                original_bulk = purchase.bulk

                # Get form data
                purchaser_id = request.POST.get('purchaser')
                product_id = request.POST.get('product')
                quantity = request.POST.get('quantity')

                # Debug the checkbox values
                debt_value = request.POST.get('debt')
                bulk_value = request.POST.get('bulk')

                # Handle checkbox values more robustly
                debt_check = debt_value == 'on' or debt_value == 'true' or debt_value == '1'
                bulk_check = bulk_value == 'on' or bulk_value == 'true' or bulk_value == '1'

                # Add debug messages
                print(f"Edit - Debt checkbox value: {debt_value}, Interpreted as: {debt_check}")
                print(f"Edit - Bulk checkbox value: {bulk_value}, Interpreted as: {bulk_check}")

                date_str = request.POST.get('date')

                # Validate required fields
                if not all([purchaser_id, product_id, quantity]):
                    messages.error(request, 'Purchaser, product, and quantity are required')
                    return redirect('purchases_view')

                try:
                    # Convert quantity to integer
                    quantity = int(quantity)
                    if quantity <= 0:
                        messages.error(request, 'Quantity must be greater than zero')
                        return redirect('purchases_view')

                    # Check if purchaser and product exist
                    purchaser = models.Purchaser.objects.get(id=purchaser_id)
                    product = models.Product.objects.get(id=product_id)

                    # Handle stock changes
                    if product.id == original_product.id:
                        # Same product, just update quantity difference
                        try:
                            stock = models.Stock.objects.get(product=product)
                            quantity_diff = quantity - original_quantity

                            if quantity_diff > 0 and stock.quantity_total < quantity_diff:
                                messages.error(request, f'Not enough stock. Available: {stock.quantity_total}')
                                return redirect('purchases_view')

                            stock.quantity_total -= quantity_diff
                            stock.save()
                        except models.Stock.DoesNotExist:
                            if quantity_diff > 0:
                                messages.error(request, 'Product is not in stock')
                                return redirect('purchases_view')
                    else:
                        # Different product, restore old stock and reduce new stock
                        try:
                            # Restore original product stock
                            old_stock = models.Stock.objects.get(product=original_product)
                            old_stock.quantity_total += original_quantity
                            old_stock.save()
                        except models.Stock.DoesNotExist:
                            # Create stock for original product if it doesn't exist
                            old_stock = models.Stock(product=original_product, quantity_total=original_quantity)
                            old_stock.save()

                        try:
                            # Reduce new product stock
                            new_stock = models.Stock.objects.get(product=product)
                            if new_stock.quantity_total < quantity:
                                messages.error(request, f'Not enough stock for new product. Available: {new_stock.quantity_total}')
                                return redirect('purchases_view')

                            new_stock.quantity_total -= quantity
                            new_stock.save()
                        except models.Stock.DoesNotExist:
                            messages.error(request, 'New product is not in stock')
                            return redirect('purchases_view')

                    # Handle purchaser amount changes with a completely new approach
                    try:
                        # STEP 1: Get the current state before any changes
                        print("\n--- EDIT PURCHASE: STARTING AMOUNT CALCULATION ---")

                        # Get fresh copies of purchasers to avoid stale data
                        current_purchaser = models.Purchaser.objects.get(id=purchase.purchaser.id)
                        new_purchaser = models.Purchaser.objects.get(id=purchaser.id)

                        print(f"Current purchaser: {current_purchaser.name}, ID: {current_purchaser.id}")
                        print(f"New purchaser: {new_purchaser.name}, ID: {new_purchaser.id}")
                        print(f"Current purchaser starting amount: {current_purchaser.amount}")

                        if current_purchaser.id != new_purchaser.id:
                            print(f"New purchaser starting amount: {new_purchaser.amount}")

                        # STEP 2: Calculate the original purchase amount
                        original_price = Decimal(original_product.bulk_price) if original_bulk else Decimal(original_product.price)
                        original_purchase_amount = original_price * original_quantity

                        print(f"Original product: {original_product.name}, Price: {original_price}")
                        print(f"Original quantity: {original_quantity}")
                        print(f"Original purchase amount: {original_purchase_amount}")

                        # STEP 3: Calculate the new purchase amount
                        new_price = Decimal(product.bulk_price) if bulk_check else Decimal(product.price)
                        new_purchase_amount = new_price * quantity

                        print(f"New product: {product.name}, Price: {new_price}")
                        print(f"New quantity: {quantity}")
                        print(f"New purchase amount: {new_purchase_amount}")

                        # STEP 4: Update the purchaser amounts

                        # First, remove the original purchase amount from the current purchaser
                        current_purchaser.amount -= original_purchase_amount
                        print(f"Current purchaser amount after removing original purchase: {current_purchaser.amount}")

                        # If the purchaser is changing, add the new purchase amount to the new purchaser
                        if current_purchaser.id != new_purchaser.id:
                            new_purchaser.amount += new_purchase_amount
                            print(f"New purchaser amount after adding new purchase: {new_purchaser.amount}")
                        else:
                            # Otherwise, add the new purchase amount to the current purchaser
                            current_purchaser.amount += new_purchase_amount
                            print(f"Current purchaser amount after adding new purchase: {current_purchaser.amount}")

                        # STEP 5: Check if any amount exceeds the maximum allowed
                        max_digits = models.Purchaser._meta.get_field('amount').max_digits
                        decimal_places = models.Purchaser._meta.get_field('amount').decimal_places
                        max_value = Decimal('9' * (max_digits - decimal_places) + '.' + '9' * decimal_places)

                        if current_purchaser.amount > max_value:
                            messages.error(request, f'Amount would exceed maximum allowed amount of {max_value} for {current_purchaser.name}.')
                            return redirect('purchases_view')

                        if current_purchaser.id != new_purchaser.id and new_purchaser.amount > max_value:
                            messages.error(request, f'Amount would exceed maximum allowed amount of {max_value} for {new_purchaser.name}.')
                            return redirect('purchases_view')

                        # STEP 6: Save the purchasers
                        current_purchaser.save()
                        if current_purchaser.id != new_purchaser.id:
                            new_purchaser.save()

                        # STEP 7: Verify the updates
                        updated_current = models.Purchaser.objects.get(id=current_purchaser.id)
                        print(f"Current purchaser final amount: {updated_current.amount}")

                        if current_purchaser.id != new_purchaser.id:
                            updated_new = models.Purchaser.objects.get(id=new_purchaser.id)
                            print(f"New purchaser final amount: {updated_new.amount}")

                        # For the view's purchaser reference, use the new_purchaser
                        target_purchaser = new_purchaser

                        # STEP 8: Add a success message
                        if current_purchaser.id == new_purchaser.id:
                            change_amount = new_purchase_amount - original_purchase_amount
                            if change_amount >= 0:
                                action = "increased"
                            else:
                                action = "decreased"
                                change_amount = abs(change_amount)

                            messages.success(request, f'Purchase updated. Amount {action} by ${change_amount} for {current_purchaser.name}. New total: ${current_purchaser.amount}')
                        else:
                            messages.success(request, f'Purchase updated. Removed ${original_purchase_amount} from {current_purchaser.name} and added ${new_purchase_amount} to {new_purchaser.name}.')

                        print("--- EDIT PURCHASE: AMOUNT CALCULATION COMPLETED ---\n")
                    except InvalidOperation as e:
                        messages.error(request, f'Error processing price values: {str(e)}')
                        return redirect('purchases_view')

                    # Update purchase with the target_purchaser
                    purchase.purchaser = target_purchaser  # Use target_purchaser instead of purchaser
                    purchase.product = product
                    purchase.quantity = quantity
                    purchase.debt = debt_check
                    purchase.bulk = bulk_check

                    # Update date if provided
                    if date_str:
                        try:
                            from datetime import datetime
                            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                            purchase.date = date_obj
                        except ValueError:
                            messages.warning(request, 'Invalid date format, using original date')

                    # Save the purchase
                    purchase.save()

                    messages.success(request, 'The Purchase edited successfully')
                except models.Purchaser.DoesNotExist:
                    messages.error(request, 'Purchaser not found')
                except models.Product.DoesNotExist:
                    messages.error(request, 'Product not found')
                except ValueError:
                    messages.error(request, 'Invalid quantity value')
                except InvalidOperation as e:
                    messages.error(request, f'Error processing decimal values: {str(e)}. Please check the price values.')
                except Exception as e:
                    messages.error(request, f'Error editing purchase: {str(e)}')
            except models.Purchases.DoesNotExist:
                messages.error(request, 'Purchase not found')

            return redirect('purchases_view')
        return redirect('purchases_view')
    return redirect('login_view')




def purchases_remove_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('purchase_id')

            # Check if purchase exists
            try:
                if not id:
                    messages.error(request, 'Purchase ID is missing')
                    return redirect('purchases_view')

                purchase = models.Purchases.objects.get(id=id)

                # Restore stock quantity
                try:
                    stock = models.Stock.objects.get(product=purchase.product)
                    stock.quantity_total += purchase.quantity
                    stock.save()
                except models.Stock.DoesNotExist:
                    # Create stock entry if it doesn't exist
                    stock = models.Stock(product=purchase.product, quantity_total=purchase.quantity)
                    stock.save()

                # Always update purchaser's amount, regardless of debt status
                try:
                    # Debug the purchase status
                    print(f"Remove - Purchase debt status: {purchase.debt}")

                    price = Decimal(purchase.product.bulk_price) if purchase.bulk else Decimal(purchase.product.price)
                    total = price * purchase.quantity

                    # Debug the amount calculation
                    print(f"Remove - Current purchaser amount: {purchase.purchaser.amount}")
                    print(f"Remove - Total price to subtract: {total}")

                    # Calculate new amount
                    new_amount = purchase.purchaser.amount - total
                    print(f"Remove - New amount calculated: {new_amount}")

                    # Update purchaser amount
                    purchase.purchaser.amount = new_amount
                    purchase.purchaser.save()

                    # Verify the update
                    updated_purchaser = models.Purchaser.objects.get(id=purchase.purchaser.id)
                    print(f"Remove - Purchaser amount after save: {updated_purchaser.amount}")

                    # Add a success message about the amount update
                    if purchase.debt:
                        messages.success(request, f'Debt of ${total} removed from {purchase.purchaser.name}. New total: ${new_amount}')
                    else:
                        messages.success(request, f'Amount of ${total} removed from {purchase.purchaser.name}. New total: ${new_amount}')
                except InvalidOperation as e:
                    messages.error(request, f'Error processing price: {str(e)}')
                    return redirect('purchases_view')

                # Delete purchase
                purchase.delete()
                messages.success(request, 'The purchase is deleted successfully')
            except models.Purchases.DoesNotExist:
                messages.error(request, f'Purchase with ID {id} not found')
            except InvalidOperation as e:
                messages.error(request, f'Error processing decimal values: {str(e)}. Please check the price values.')
            except Exception as e:
                messages.error(request, f'Error deleting purchase: {str(e)}')

            return redirect('purchases_view')
        return redirect('purchases_view')
    return redirect('login_view')


def expense_view(request):
    if request.user.is_authenticated:
        # Get all providers and products for filter dropdowns
        providers = models.Provider.objects.all()
        products = models.Product.objects.all()

        # Calculate max price value from model definition
        max_digits = models.Expense._meta.get_field('price').max_digits
        decimal_places = models.Expense._meta.get_field('price').decimal_places
        max_price = Decimal('9' * (max_digits - decimal_places) + '.' + '9' * decimal_places)

        # Start with all expenses
        expenses_query = models.Expense.objects.all()

        # Check if any filter parameters are provided (works for both GET and POST)
        filter_params = request.GET if request.method == 'GET' else request.POST

        provider_id = filter_params.get('provider')
        product_id = filter_params.get('product')
        date_str = filter_params.get('date')

        # Track if any filters were applied
        filters_applied = False

        # Apply filters if provided
        if provider_id:
            expenses_query = expenses_query.filter(provider_id=provider_id)
            filters_applied = True

        if product_id:
            expenses_query = expenses_query.filter(product_id=product_id)
            filters_applied = True

        if date_str and date_str.strip():
            # Convert date string to date object
            try:
                from datetime import datetime
                date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                expenses_query = expenses_query.filter(date__date=date_obj)
                filters_applied = True
            except (ValueError, TypeError):
                messages.error(request, 'Invalid date format')

        # Add a message if filters were applied
        if filters_applied:
            filter_description = []
            if provider_id:
                try:
                    provider = models.Provider.objects.get(id=provider_id)
                    filter_description.append(f"Provider: {provider.name}")
                except:
                    pass
            if product_id:
                try:
                    product = models.Product.objects.get(id=product_id)
                    filter_description.append(f"Product: {product.name}")
                except:
                    pass
            if date_str and date_str.strip():
                filter_description.append(f"Date: {date_str}")

            if filter_description:
                messages.info(request, f"Showing filtered results for {', '.join(filter_description)}")

        context = {
            'expenses': expenses_query,
            'providers': providers,
            'products': products,
            'max_price': max_price,
            # Pre-select the filter values in the form
            'selected_provider': provider_id,
            'selected_product': product_id,
            'selected_date': date_str
        }
        return render(request, 'expense.html', context=context)
    return redirect('login_view')


def expense_add_view(request):
    if request.user.is_authenticated:
        # Get all providers and products for dropdowns
        providers = models.Provider.objects.all()
        products = models.Product.objects.all()

        # Calculate max price value from model definition
        max_digits = models.Expense._meta.get_field('price').max_digits
        decimal_places = models.Expense._meta.get_field('price').decimal_places
        max_price = Decimal('9' * (max_digits - decimal_places) + '.' + '9' * decimal_places)

        context = {
            'providers': providers,
            'products': products,
            'max_price': max_price
        }

        if request.method == 'POST':
            provider_id = request.POST.get('provider')
            product_id = request.POST.get('product')
            quantity = request.POST.get('quantity')
            price = request.POST.get('price')

            # Validate required fields
            if not all([provider_id, product_id, quantity, price]):
                messages.error(request, 'Provider, product, quantity, and price are required')
                return render(request, 'expense_add.html', context=context)

            try:
                # Convert quantity and price to appropriate types
                quantity = int(quantity)

                # Handle price conversion more carefully
                try:
                    # First, try direct conversion
                    price = Decimal(price)
                except InvalidOperation:
                    # If that fails, try to clean the string and convert again
                    price_str = price.replace(',', '.').strip()
                    price = Decimal(price_str)

                if quantity <= 0:
                    messages.error(request, 'Quantity must be greater than zero')
                    return render(request, 'expense_add.html', context=context)

                if price <= 0:
                    messages.error(request, 'Price must be greater than zero')
                    return render(request, 'expense_add.html', context=context)

                # Check if price exceeds the maximum allowed by the model
                max_digits = models.Expense._meta.get_field('price').max_digits
                decimal_places = models.Expense._meta.get_field('price').decimal_places
                max_value = Decimal('9' * (max_digits - decimal_places) + '.' + '9' * decimal_places)

                if price > max_value:
                    messages.error(request, f'Price must be less than {max_value}')
                    return render(request, 'expense_add.html', context=context)

                # Check if provider and product exist
                try:
                    provider = models.Provider.objects.get(id=provider_id)
                    product = models.Product.objects.get(id=product_id)

                    # Create expense
                    expense = models.Expense(
                        provider=provider,
                        product=product,
                        quantity=quantity,
                        price=price
                    )
                    expense.save()

                    # Update or create stock entry
                    try:
                        stock = models.Stock.objects.get(product=product)
                        stock.quantity_total += quantity
                        stock.save()
                    except models.Stock.DoesNotExist:
                        stock = models.Stock(product=product, quantity_total=quantity)
                        stock.save()

                    messages.success(request, 'The expense is added successfully')
                    return redirect('expense_view')
                except models.Provider.DoesNotExist:
                    messages.error(request, 'Provider not found')
                except models.Product.DoesNotExist:
                    messages.error(request, 'Product not found')
            except ValueError:
                messages.error(request, 'Invalid quantity or price values')
            except Exception as e:
                # Calculate max price value from model definition
                max_digits = models.Expense._meta.get_field('price').max_digits
                decimal_places = models.Expense._meta.get_field('price').decimal_places
                max_price = Decimal('9' * (max_digits - decimal_places) + '.' + '9' * decimal_places)
                messages.error(request, f'Error adding expense: {str(e)}. Please ensure the price is a valid number less than {max_price}.')

            return render(request, 'expense_add.html', context=context)

        # GET request - show form
        return render(request, 'expense_add.html', context=context)
    return redirect('login_view')

def expense_edit_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('expense_id')

            # Check if expense exists
            try:
                expense = models.Expense.objects.get(id=id)

                # Get original values for comparison
                original_quantity = expense.quantity
                original_product = expense.product

                # Get form data
                provider_id = request.POST.get('provider')
                product_id = request.POST.get('product')
                quantity = request.POST.get('quantity')
                price = request.POST.get('price')

                # Validate required fields
                if not all([provider_id, product_id, quantity, price]):
                    messages.error(request, 'Provider, product, quantity, and price are required')
                    return redirect('expense_view')

                try:
                    # Convert quantity and price to appropriate types
                    quantity = int(quantity)

                    # Handle price conversion more carefully
                    try:
                        # First, try direct conversion
                        price = Decimal(price)
                    except InvalidOperation:
                        # If that fails, try to clean the string and convert again
                        price_str = price.replace(',', '.').strip()
                        price = Decimal(price_str)

                    if quantity <= 0:
                        messages.error(request, 'Quantity must be greater than zero')
                        return redirect('expense_view')

                    if price <= 0:
                        messages.error(request, 'Price must be greater than zero')
                        return redirect('expense_view')

                    # Check if price exceeds the maximum allowed by the model
                    max_digits = models.Expense._meta.get_field('price').max_digits
                    decimal_places = models.Expense._meta.get_field('price').decimal_places
                    max_value = Decimal('9' * (max_digits - decimal_places) + '.' + '9' * decimal_places)

                    if price > max_value:
                        messages.error(request, f'Price must be less than {max_value}')
                        return redirect('expense_view')

                    # Check if provider and product exist
                    provider = models.Provider.objects.get(id=provider_id)
                    product = models.Product.objects.get(id=product_id)

                    # Handle stock changes
                    if product.id == original_product.id:
                        # Same product, just update quantity difference
                        try:
                            stock = models.Stock.objects.get(product=product)
                            quantity_diff = quantity - original_quantity
                            stock.quantity_total += quantity_diff
                            stock.save()
                        except models.Stock.DoesNotExist:
                            # Create stock if it doesn't exist
                            stock = models.Stock(product=product, quantity_total=quantity)
                            stock.save()
                    else:
                        # Different product, update both stocks
                        try:
                            # Reduce original product stock
                            old_stock = models.Stock.objects.get(product=original_product)
                            old_stock.quantity_total -= original_quantity
                            old_stock.save()
                        except models.Stock.DoesNotExist:
                            pass  # No stock entry to update

                        try:
                            # Increase new product stock
                            new_stock = models.Stock.objects.get(product=product)
                            new_stock.quantity_total += quantity
                            new_stock.save()
                        except models.Stock.DoesNotExist:
                            # Create stock for new product
                            new_stock = models.Stock(product=product, quantity_total=quantity)
                            new_stock.save()

                    # Update expense
                    expense.provider = provider
                    expense.product = product
                    expense.quantity = quantity
                    expense.price = price
                    expense.save()

                    messages.success(request, 'The expense is edited successfully')
                except models.Provider.DoesNotExist:
                    messages.error(request, 'Provider not found')
                except models.Product.DoesNotExist:
                    messages.error(request, 'Product not found')
                except ValueError:
                    messages.error(request, 'Invalid quantity or price values')
                except Exception as e:
                    # Calculate max price value from model definition
                    max_digits = models.Expense._meta.get_field('price').max_digits
                    decimal_places = models.Expense._meta.get_field('price').decimal_places
                    max_price = Decimal('9' * (max_digits - decimal_places) + '.' + '9' * decimal_places)
                    messages.error(request, f'Error editing expense: {str(e)}. Please ensure the price is a valid number less than {max_price}.')
            except models.Expense.DoesNotExist:
                messages.error(request, 'Expense not found')

            return redirect('expense_view')
        return redirect('expense_view')
    return redirect('login_view')

def expense_remove_view(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            id = request.POST.get('expense_id')

            # Check if expense exists
            try:
                expense = models.Expense.objects.get(id=id)

                # Update stock by reducing the quantity
                try:
                    stock = models.Stock.objects.get(product=expense.product)
                    stock.quantity_total -= expense.quantity

                    # Ensure stock doesn't go negative
                    if stock.quantity_total < 0:
                        stock.quantity_total = 0

                    stock.save()
                except models.Stock.DoesNotExist:
                    pass  # No stock to update

                # Delete expense
                expense.delete()
                messages.success(request, 'The expense is removed successfully')
            except models.Expense.DoesNotExist:
                messages.error(request, 'Expense not found')
            except Exception as e:
                messages.error(request, f'Error removing expense: {str(e)}')

            return redirect('expense_view')
        return redirect('expense_view')
    return redirect('login_view')


def login_view(request):
    # If user is already authenticated, redirect to dashboard
    if request.user.is_authenticated:
        return redirect('dashboard')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        # Validate input
        if not username or not password:
            language_code = request.session.get('django_language', 'en')
            messages.error(request, get_translation('Username and password are required', language_code))
            return render(request, 'login.html')

        user = authenticate(username=username, password=password)
        if user:
            login(request, user)
            language_code = request.session.get('django_language', 'en')
            messages.success(request, get_translation('Login successful. Welcome back!', language_code))
            return redirect('dashboard')
        else:
            language_code = request.session.get('django_language', 'en')
            messages.error(request, get_translation('Invalid username or password', language_code))

    return render(request, 'login.html')

def register_view(request):
    # If user is already authenticated, redirect to dashboard
    if request.user.is_authenticated:
        return redirect('dashboard')

    form = forms.RegisterForm()
    if request.method == 'POST':
        form = forms.RegisterForm(request.POST)
        if form.is_valid():
            username = request.POST.get('username')
            password = request.POST.get('password')

            # Create new user
            try:
                user = models.Employer(username=username)
                user.set_password(password)
                user.save()

                # Authenticate and login the new user
                user = authenticate(request, username=username, password=password)
                if user:
                    login(request, user)
                    language_code = request.session.get('django_language', 'en')
                    messages.success(request, get_translation('Registration successful!', language_code))
                    return redirect('dashboard')
                else:
                    language_code = request.session.get('django_language', 'en')
                    messages.error(request, get_translation('Error during authentication', language_code))
                    return redirect('register_view')
            except Exception as e:
                language_code = request.session.get('django_language', 'en')
                messages.error(request, get_translation('Error creating account:', language_code) + f' {str(e)}')
                return redirect('register_view')
        else:
            # Display form validation errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"{field}: {error}")

    return render(request, 'register.html', context={'form': form})

def logout_view(request):
    if request.user.is_authenticated:
        logout(request)
        language_code = request.session.get('django_language', 'en')
        messages.success(request, get_translation('Logout successful', language_code))
    return redirect('login_view')

def set_language_view(request):
    """Custom view to handle language switching"""
    from django.http import HttpResponseRedirect

    language = request.GET.get('language', 'en')
    if language in ['en', 'ar']:
        # Use the correct session key for language
        request.session['django_language'] = language

    # Redirect to the same page or login page
    next_url = request.GET.get('next', '/login/')
    return HttpResponseRedirect(next_url)

def profile_view(request):
    """View for user profile page"""
    if request.user.is_authenticated:
        if request.method == 'POST':
            # Handle profile update
            first_name = request.POST.get('first_name', '')
            last_name = request.POST.get('last_name', '')
            email = request.POST.get('email', '')

            # Update user information
            request.user.first_name = first_name
            request.user.last_name = last_name
            request.user.email = email
            request.user.save()

            language_code = request.session.get('django_language', 'en')
            messages.success(request, get_translation('Profile updated successfully', language_code))
            return redirect('profile_view')

        # Get statistics for the profile page
        products_count = models.Product.objects.count()
        purchases_count = models.Purchases.objects.count()

        # Get user's two-factor authentication status
        try:
            from django_otp import user_has_device
            has_2fa = user_has_device(request.user)
        except Exception as e:
            # If there's an issue with 2FA check, default to False
            has_2fa = False
        
        language_code = request.session.get('django_language', 'en')
        context = {
            'user': request.user,
            'title': get_translation('User Profile', language_code),
            'products_count': products_count,
            'purchases_count': purchases_count,
            'has_2fa': has_2fa
        }
        return render(request, 'profile.html', context=context)
    return redirect('login_view')

def settings_view(request):
    """View for user settings page"""
    if request.user.is_authenticated:
        if request.method == 'POST':
            # Handle language change
            language = request.POST.get('language')
            if language in ['en', 'ar']:
                request.session['django_language'] = language
                language_code = request.session.get('django_language', 'en')
                messages.success(request, get_translation('Language changed successfully', language_code))

            # Handle password change
            if 'change_password' in request.POST:
                current_password = request.POST.get('current_password')
                new_password = request.POST.get('new_password')
                confirm_password = request.POST.get('confirm_password')
                language_code = request.session.get('django_language', 'en')

                if request.user.check_password(current_password):
                    if new_password == confirm_password:
                        if len(new_password) >= 8:
                            request.user.set_password(new_password)
                            request.user.save()
                            messages.success(request, get_translation('Password changed successfully', language_code))
                            # Re-authenticate user after password change
                            from django.contrib.auth import update_session_auth_hash
                            update_session_auth_hash(request, request.user)
                        else:
                            messages.error(request, get_translation('Password must be at least 8 characters long', language_code))
                    else:
                        messages.error(request, get_translation('New passwords do not match', language_code))
                else:
                    messages.error(request, get_translation('Current password is incorrect', language_code))

            return redirect('settings_view')

        language_code = request.session.get('django_language', 'en')
        context = {
            'title': get_translation('Settings', language_code),
            'current_language': request.session.get('django_language', 'en')
        }
        return render(request, 'settings.html', context=context)
    return redirect('login_view')



# All syntax and logic errors have been fixed
# Authentication checks added to all views
# Stock management implemented for purchases and expenses
# Form validation added to all forms