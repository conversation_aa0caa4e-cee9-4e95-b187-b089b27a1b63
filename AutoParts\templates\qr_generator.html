{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "QR Code Generator" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-qrcode fa-fw mr-2"></i>{% custom_trans "QR Code Generator" %}
        </h1>
        <a href="javascript:history.back()" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> {% custom_trans "Back" %}
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-magic fa-fw mr-2"></i>{% custom_trans "Generate QR Code" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <!-- Input Form -->
                            <div class="card bg-light mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-edit fa-fw mr-2"></i>{% custom_trans "Enter Content" %}
                                    </h5>
                                    
                                    <!-- Content Type Selection -->
                                    <div class="form-group">
                                        <label for="content-type">{% custom_trans "Content Type" %}:</label>
                                        <select id="content-type" class="form-control">
                                            <option value="text">{% custom_trans "Plain Text" %}</option>
                                            <option value="url">{% custom_trans "Website URL" %}</option>
                                            <option value="email">{% custom_trans "Email Address" %}</option>
                                            <option value="phone">{% custom_trans "Phone Number" %}</option>
                                            <option value="sms">{% custom_trans "SMS Message" %}</option>
                                            <option value="wifi">{% custom_trans "WiFi Network" %}</option>
                                            <option value="vcard">{% custom_trans "Contact Card" %}</option>
                                        </select>
                                    </div>

                                    <!-- Text Input -->
                                    <div id="text-input" class="content-input">
                                        <div class="form-group">
                                            <label for="text-content">{% custom_trans "Text Content" %}:</label>
                                            <textarea id="text-content" class="form-control" rows="4" placeholder="{% custom_trans 'Enter your text here...' %}"></textarea>
                                        </div>
                                    </div>

                                    <!-- URL Input -->
                                    <div id="url-input" class="content-input" style="display: none;">
                                        <div class="form-group">
                                            <label for="url-content">{% custom_trans "Website URL" %}:</label>
                                            <input type="url" id="url-content" class="form-control" placeholder="https://example.com">
                                        </div>
                                    </div>

                                    <!-- Email Input -->
                                    <div id="email-input" class="content-input" style="display: none;">
                                        <div class="form-group">
                                            <label for="email-content">{% custom_trans "Email Address" %}:</label>
                                            <input type="email" id="email-content" class="form-control" placeholder="<EMAIL>">
                                        </div>
                                        <div class="form-group">
                                            <label for="email-subject">{% custom_trans "Subject" %} ({% custom_trans "optional" %}):</label>
                                            <input type="text" id="email-subject" class="form-control" placeholder="{% custom_trans 'Email subject' %}">
                                        </div>
                                        <div class="form-group">
                                            <label for="email-body">{% custom_trans "Message" %} ({% custom_trans "optional" %}):</label>
                                            <textarea id="email-body" class="form-control" rows="3" placeholder="{% custom_trans 'Email message' %}"></textarea>
                                        </div>
                                    </div>

                                    <!-- Phone Input -->
                                    <div id="phone-input" class="content-input" style="display: none;">
                                        <div class="form-group">
                                            <label for="phone-content">{% custom_trans "Phone Number" %}:</label>
                                            <input type="tel" id="phone-content" class="form-control" placeholder="+1234567890">
                                        </div>
                                    </div>

                                    <!-- SMS Input -->
                                    <div id="sms-input" class="content-input" style="display: none;">
                                        <div class="form-group">
                                            <label for="sms-phone">{% custom_trans "Phone Number" %}:</label>
                                            <input type="tel" id="sms-phone" class="form-control" placeholder="+1234567890">
                                        </div>
                                        <div class="form-group">
                                            <label for="sms-message">{% custom_trans "Message" %}:</label>
                                            <textarea id="sms-message" class="form-control" rows="3" placeholder="{% custom_trans 'SMS message' %}"></textarea>
                                        </div>
                                    </div>

                                    <!-- WiFi Input -->
                                    <div id="wifi-input" class="content-input" style="display: none;">
                                        <div class="form-group">
                                            <label for="wifi-ssid">{% custom_trans "Network Name (SSID)" %}:</label>
                                            <input type="text" id="wifi-ssid" class="form-control" placeholder="{% custom_trans 'WiFi network name' %}">
                                        </div>
                                        <div class="form-group">
                                            <label for="wifi-password">{% custom_trans "Password" %}:</label>
                                            <input type="password" id="wifi-password" class="form-control" placeholder="{% custom_trans 'WiFi password' %}">
                                        </div>
                                        <div class="form-group">
                                            <label for="wifi-security">{% custom_trans "Security Type" %}:</label>
                                            <select id="wifi-security" class="form-control">
                                                <option value="WPA">WPA/WPA2</option>
                                                <option value="WEP">WEP</option>
                                                <option value="nopass">{% custom_trans "No Password" %}</option>
                                            </select>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="wifi-hidden">
                                            <label class="form-check-label" for="wifi-hidden">
                                                {% custom_trans "Hidden Network" %}
                                            </label>
                                        </div>
                                    </div>

                                    <!-- vCard Input -->
                                    <div id="vcard-input" class="content-input" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="vcard-firstname">{% custom_trans "First Name" %}:</label>
                                                    <input type="text" id="vcard-firstname" class="form-control" placeholder="{% custom_trans 'First name' %}">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="vcard-lastname">{% custom_trans "Last Name" %}:</label>
                                                    <input type="text" id="vcard-lastname" class="form-control" placeholder="{% custom_trans 'Last name' %}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="vcard-phone">{% custom_trans "Phone" %}:</label>
                                            <input type="tel" id="vcard-phone" class="form-control" placeholder="+1234567890">
                                        </div>
                                        <div class="form-group">
                                            <label for="vcard-email">{% custom_trans "Email" %}:</label>
                                            <input type="email" id="vcard-email" class="form-control" placeholder="<EMAIL>">
                                        </div>
                                        <div class="form-group">
                                            <label for="vcard-organization">{% custom_trans "Organization" %}:</label>
                                            <input type="text" id="vcard-organization" class="form-control" placeholder="{% custom_trans 'Company name' %}">
                                        </div>
                                        <div class="form-group">
                                            <label for="vcard-title">{% custom_trans "Job Title" %}:</label>
                                            <input type="text" id="vcard-title" class="form-control" placeholder="{% custom_trans 'Job title' %}">
                                        </div>
                                    </div>

                                    <!-- QR Code Settings -->
                                    <hr>
                                    <h6 class="text-gray-900 mb-3">
                                        <i class="fas fa-cog fa-fw mr-2"></i>{% custom_trans "QR Code Settings" %}
                                    </h6>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="qr-size">{% custom_trans "Size" %}:</label>
                                                <select id="qr-size" class="form-control">
                                                    <option value="200">200x200 px</option>
                                                    <option value="300" selected>300x300 px</option>
                                                    <option value="400">400x400 px</option>
                                                    <option value="500">500x500 px</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="qr-color">{% custom_trans "Color" %}:</label>
                                                <input type="color" id="qr-color" class="form-control" value="#000000">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Generate Button -->
                                    <button id="generate-btn" class="btn btn-primary btn-block">
                                        <i class="fas fa-magic fa-fw mr-2"></i>{% custom_trans "Generate QR Code" %}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <!-- QR Code Display -->
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">
                                        <i class="fas fa-qrcode fa-fw mr-2"></i>{% custom_trans "Generated QR Code" %}
                                    </h5>
                                    
                                    <div id="qr-display" class="mb-3">
                                        <div class="qr-placeholder">
                                            <i class="fas fa-qrcode fa-5x text-gray-300 mb-3"></i>
                                            <p class="text-gray-500">{% custom_trans "Your QR code will appear here" %}</p>
                                        </div>
                                    </div>

                                    <!-- Download Options -->
                                    <div id="download-options" style="display: none;">
                                        <div class="btn-group-vertical w-100">
                                            <button id="download-png" class="btn btn-success mb-2">
                                                <i class="fas fa-download fa-fw mr-2"></i>{% custom_trans "Download PNG" %}
                                            </button>
                                            <button id="download-svg" class="btn btn-info mb-2">
                                                <i class="fas fa-download fa-fw mr-2"></i>{% custom_trans "Download SVG" %}
                                            </button>
                                            <button id="copy-image" class="btn btn-secondary">
                                                <i class="fas fa-copy fa-fw mr-2"></i>{% custom_trans "Copy Image" %}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="card bg-light mt-3">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-bolt fa-fw mr-2"></i>{% custom_trans "Quick Actions" %}
                                    </h6>
                                    <div class="btn-group-vertical w-100">
                                        <button id="clear-all" class="btn btn-outline-warning btn-sm mb-2">
                                            <i class="fas fa-trash fa-fw mr-2"></i>{% custom_trans "Clear All" %}
                                        </button>
                                        <a href="{% url 'qr_scanner_view' %}" class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-camera fa-fw mr-2"></i>{% custom_trans "Scan QR Code" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Library -->
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

<style>
.card {
    border: none;
    border-radius: 0.5rem;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h6 {
    color: white !important;
}

.qr-placeholder {
    padding: 2rem;
    border: 2px dashed #e3e6f0;
    border-radius: 0.5rem;
    background: #f8f9fc;
}

#qr-display canvas,
#qr-display svg {
    max-width: 100%;
    height: auto;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    background: white;
    padding: 10px;
}

.content-input {
    transition: all 0.3s ease;
}

.btn-group-vertical .btn {
    border-radius: 0.35rem !important;
    margin-bottom: 0.25rem;
}

@media (max-width: 768px) {
    .btn-group-vertical .btn {
        font-size: 0.9rem;
        padding: 0.5rem;
    }
}
</style>

<script>
let currentQRCode = null;

document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

function setupEventListeners() {
    // Content type change
    document.getElementById('content-type').addEventListener('change', handleContentTypeChange);
    
    // Generate button
    document.getElementById('generate-btn').addEventListener('click', generateQRCode);
    
    // Download buttons
    document.getElementById('download-png').addEventListener('click', downloadPNG);
    document.getElementById('download-svg').addEventListener('click', downloadSVG);
    document.getElementById('copy-image').addEventListener('click', copyImage);
    
    // Clear button
    document.getElementById('clear-all').addEventListener('click', clearAll);
    
    // Auto-generate on input change
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        if (input.id !== 'content-type') {
            input.addEventListener('input', debounce(generateQRCode, 500));
        }
    });
}

function handleContentTypeChange() {
    const contentType = document.getElementById('content-type').value;
    
    // Hide all content inputs
    document.querySelectorAll('.content-input').forEach(input => {
        input.style.display = 'none';
    });
    
    // Show selected content input
    document.getElementById(contentType + '-input').style.display = 'block';
    
    // Clear QR code
    clearQRCode();
}

function generateQRCode() {
    const content = getContentBasedOnType();
    
    if (!content.trim()) {
        clearQRCode();
        return;
    }
    
    const size = parseInt(document.getElementById('qr-size').value);
    const color = document.getElementById('qr-color').value;
    
    const options = {
        width: size,
        height: size,
        color: {
            dark: color,
            light: '#FFFFFF'
        },
        margin: 2
    };
    
    // Clear previous QR code
    const qrDisplay = document.getElementById('qr-display');
    qrDisplay.innerHTML = '';
    
    // Generate new QR code
    QRCode.toCanvas(content, options, function(error, canvas) {
        if (error) {
            console.error('Error generating QR code:', error);
            showError('{% custom_trans "Error generating QR code" %}');
            return;
        }
        
        qrDisplay.appendChild(canvas);
        currentQRCode = { content, canvas, options };
        
        // Show download options
        document.getElementById('download-options').style.display = 'block';
    });
}

function getContentBasedOnType() {
    const contentType = document.getElementById('content-type').value;
    
    switch (contentType) {
        case 'text':
            return document.getElementById('text-content').value;
            
        case 'url':
            return document.getElementById('url-content').value;
            
        case 'email':
            const email = document.getElementById('email-content').value;
            const subject = document.getElementById('email-subject').value;
            const body = document.getElementById('email-body').value;
            let emailContent = `mailto:${email}`;
            const params = [];
            if (subject) params.push(`subject=${encodeURIComponent(subject)}`);
            if (body) params.push(`body=${encodeURIComponent(body)}`);
            if (params.length > 0) emailContent += `?${params.join('&')}`;
            return emailContent;
            
        case 'phone':
            return `tel:${document.getElementById('phone-content').value}`;
            
        case 'sms':
            const smsPhone = document.getElementById('sms-phone').value;
            const smsMessage = document.getElementById('sms-message').value;
            return `sms:${smsPhone}${smsMessage ? `?body=${encodeURIComponent(smsMessage)}` : ''}`;
            
        case 'wifi':
            const ssid = document.getElementById('wifi-ssid').value;
            const password = document.getElementById('wifi-password').value;
            const security = document.getElementById('wifi-security').value;
            const hidden = document.getElementById('wifi-hidden').checked;
            return `WIFI:T:${security};S:${ssid};P:${password};H:${hidden ? 'true' : 'false'};;`;
            
        case 'vcard':
            const firstName = document.getElementById('vcard-firstname').value;
            const lastName = document.getElementById('vcard-lastname').value;
            const phone = document.getElementById('vcard-phone').value;
            const email = document.getElementById('vcard-email').value;
            const org = document.getElementById('vcard-organization').value;
            const title = document.getElementById('vcard-title').value;
            
            let vcard = 'BEGIN:VCARD\nVERSION:3.0\n';
            if (firstName || lastName) vcard += `FN:${firstName} ${lastName}\n`;
            if (firstName) vcard += `N:${lastName};${firstName};;;\n`;
            if (phone) vcard += `TEL:${phone}\n`;
            if (email) vcard += `EMAIL:${email}\n`;
            if (org) vcard += `ORG:${org}\n`;
            if (title) vcard += `TITLE:${title}\n`;
            vcard += 'END:VCARD';
            return vcard;
            
        default:
            return '';
    }
}

function downloadPNG() {
    if (!currentQRCode) return;
    
    const link = document.createElement('a');
    link.download = 'qrcode.png';
    link.href = currentQRCode.canvas.toDataURL();
    link.click();
}

function downloadSVG() {
    if (!currentQRCode) return;
    
    QRCode.toString(currentQRCode.content, {
        type: 'svg',
        width: currentQRCode.options.width,
        color: currentQRCode.options.color
    }, function(error, svg) {
        if (error) {
            console.error('Error generating SVG:', error);
            return;
        }
        
        const blob = new Blob([svg], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.download = 'qrcode.svg';
        link.href = url;
        link.click();
        URL.revokeObjectURL(url);
    });
}

function copyImage() {
    if (!currentQRCode) return;
    
    currentQRCode.canvas.toBlob(function(blob) {
        const item = new ClipboardItem({ 'image/png': blob });
        navigator.clipboard.write([item]).then(function() {
            showSuccess('{% custom_trans "QR code copied to clipboard!" %}');
        }).catch(function(error) {
            console.error('Error copying to clipboard:', error);
            showError('{% custom_trans "Failed to copy QR code" %}');
        });
    });
}

function clearAll() {
    // Clear all inputs
    document.querySelectorAll('input, textarea').forEach(input => {
        if (input.type !== 'color' && input.id !== 'qr-size') {
            input.value = '';
        }
    });
    
    // Reset to text type
    document.getElementById('content-type').value = 'text';
    handleContentTypeChange();
    
    clearQRCode();
}

function clearQRCode() {
    const qrDisplay = document.getElementById('qr-display');
    qrDisplay.innerHTML = `
        <div class="qr-placeholder">
            <i class="fas fa-qrcode fa-5x text-gray-300 mb-3"></i>
            <p class="text-gray-500">{% custom_trans "Your QR code will appear here" %}</p>
        </div>
    `;
    
    document.getElementById('download-options').style.display = 'none';
    currentQRCode = null;
}

function showSuccess(message) {
    // You can implement a toast notification here
    alert(message);
}

function showError(message) {
    // You can implement a toast notification here
    alert(message);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
