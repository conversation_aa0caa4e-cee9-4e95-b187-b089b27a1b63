{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Two-Factor Authentication Setup" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Two-Factor Authentication Setup" %}
        </h1>
        <a href="{% url 'profile_view' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> {% custom_trans "Back to Profile" %}
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-8 col-lg-10">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-mobile-alt fa-fw mr-2"></i>{% custom_trans "Setup Authenticator App" %}
                    </h6>
                </div>
                <div class="card-body">
                    {% if wizard.steps.current == 'welcome' %}
                        <!-- Welcome Step -->
                        <div class="text-center mb-4">
                            <div class="mb-3">
                                <i class="fas fa-shield-alt fa-5x text-primary"></i>
                            </div>
                            <h4 class="text-gray-900">{% custom_trans "Secure Your Account" %}</h4>
                            <p class="text-gray-600">
                                {% custom_trans "Two-factor authentication adds an extra layer of security to your account by requiring a verification code from your mobile device." %}
                            </p>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card border-left-success shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                    {% custom_trans "Enhanced Security" %}
                                                </div>
                                                <div class="text-xs mb-0 text-gray-800">
                                                    {% custom_trans "Protect your account from unauthorized access" %}
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-lock fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card border-left-info shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                    {% custom_trans "Easy to Use" %}
                                                </div>
                                                <div class="text-xs mb-0 text-gray-800">
                                                    {% custom_trans "Quick setup with your smartphone" %}
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-mobile-alt fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form method="post">
                            {% csrf_token %}
                            {{ wizard.management_form }}
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-right fa-fw mr-2"></i>{% custom_trans "Get Started" %}
                                </button>
                            </div>
                        </form>

                    {% elif wizard.steps.current == 'generator' %}
                        <!-- QR Code Step -->
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-gray-900 mb-3">
                                    <i class="fas fa-qrcode fa-fw mr-2"></i>{% custom_trans "Scan QR Code" %}
                                </h5>
                                <div class="text-center mb-3">
                                    <div class="p-3 bg-white border rounded">
                                        {{ qr_code|safe }}
                                    </div>
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle fa-fw mr-2"></i>
                                    <strong>{% custom_trans "Manual Entry:" %}</strong><br>
                                    <code>{{ secret_key }}</code>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5 class="text-gray-900 mb-3">
                                    <i class="fas fa-mobile-alt fa-fw mr-2"></i>{% custom_trans "Instructions" %}
                                </h5>
                                <ol class="text-gray-700">
                                    <li class="mb-2">
                                        {% custom_trans "Download an authenticator app:" %}
                                        <ul class="mt-1">
                                            <li>Google Authenticator</li>
                                            <li>Microsoft Authenticator</li>
                                            <li>Authy</li>
                                        </ul>
                                    </li>
                                    <li class="mb-2">{% custom_trans "Open the app and scan the QR code" %}</li>
                                    <li class="mb-2">{% custom_trans "Enter the 6-digit code below" %}</li>
                                </ol>

                                <div class="mt-3">
                                    <a href="{% url 'qr_scanner_view' %}" target="_blank" class="btn btn-info btn-sm">
                                        <i class="fas fa-camera fa-fw mr-2"></i>{% custom_trans "Use QR Scanner" %}
                                    </a>
                                    <small class="text-muted d-block mt-1">
                                        {% custom_trans "Open our QR scanner in a new tab to scan codes from your screen" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <form method="post">
                            {% csrf_token %}
                            {{ wizard.management_form }}
                            <div class="form-group">
                                <label for="{{ form.token.id_for_label }}" class="font-weight-bold">
                                    {% custom_trans "Verification Code" %}
                                </label>
                                {{ form.token }}
                                {% if form.token.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.token.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-check fa-fw mr-2"></i>{% custom_trans "Verify & Enable" %}
                                </button>
                            </div>
                        </form>

                    {% elif wizard.steps.current == 'backup' %}
                        <!-- Backup Tokens Step -->
                        <div class="text-center mb-4">
                            <i class="fas fa-key fa-4x text-warning mb-3"></i>
                            <h4 class="text-gray-900">{% custom_trans "Backup Tokens" %}</h4>
                            <p class="text-gray-600">
                                {% custom_trans "Save these backup tokens in a safe place. You can use them to access your account if you lose your phone." %}
                            </p>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle fa-fw mr-2"></i>
                            <strong>{% custom_trans "Important:" %}</strong> 
                            {% custom_trans "Each token can only be used once. Store them securely!" %}
                        </div>

                        <div class="row">
                            {% for token in backup_tokens %}
                                <div class="col-md-6 mb-2">
                                    <div class="card bg-light">
                                        <div class="card-body text-center py-2">
                                            <code class="h6">{{ token }}</code>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <form method="post">
                            {% csrf_token %}
                            {{ wizard.management_form }}
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-check fa-fw mr-2"></i>{% custom_trans "I've Saved My Tokens" %}
                                </button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 0.5rem;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h6 {
    color: white !important;
}

#{{ form.token.id_for_label }} {
    font-size: 1.2rem;
    text-align: center;
    letter-spacing: 0.2rem;
    padding: 0.75rem;
    border: 2px solid #e3e6f0;
    border-radius: 0.5rem;
}

#{{ form.token.id_for_label }}:focus {
    border-color: #5a5c69;
    box-shadow: 0 0 0 0.2rem rgba(90, 92, 105, 0.25);
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    border-radius: 0.5rem;
}
</style>
{% endblock %}
