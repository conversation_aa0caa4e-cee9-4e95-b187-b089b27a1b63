{% load static %}
{% load custom_i18n %}
<!DOCTYPE html>
<html lang="{% if request.session.django_language == 'ar' %}ar{% else %}en{% endif %}" dir="{% if request.session.django_language == 'ar' %}rtl{% else %}ltr{% endif %}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="{% custom_trans 'Auto Parts Management System' %}">
    <meta name="author" content="">

    <title>{% custom_trans 'Auto Parts Management System' %} - {% custom_trans 'Create Account' %}</title>

    <!-- Custom fonts for this template-->
    <link href="{% static 'sbadmin/vendor/fontawesome-free/css/all.min.css' %}" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;900&display=swap" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="{% static 'sbadmin/css/sb-admin-2.min.css' %}" rel="stylesheet">
    <!-- Custom styles for Auto Parts Management -->
    <link href="{% static 'custom.css' %}" rel="stylesheet">

    <style>
        body {
            font-family: {% if request.session.django_language == 'ar' %}'Cairo', sans-serif{% else %}'Poppins', sans-serif{% endif %} !important;
            direction: {% if request.session.django_language == 'ar' %}rtl{% else %}ltr{% endif %};
            margin: 0;
            overflow-x: hidden;
            background-color: #0a192f;
            height: 100vh;
        }

        .form-control {
            text-align: {% if request.session.django_language == 'ar' %}right{% else %}left{% endif %};
        }

        .btn {
            font-family: {% if request.session.django_language == 'ar' %}'Cairo', sans-serif{% else %}'Poppins', sans-serif{% endif %};
        }

        /* Enhanced Language switcher styles */
        .language-switcher {
            position: absolute;
            top: 30px;
            {% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 30px;
            z-index: 1000;
        }

        .language-toggle-container {
            display: flex;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .language-option {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: 18px;
            text-decoration: none;
            color: rgba(255, 255, 255, 0.7);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            font-size: 14px;
            min-width: 80px;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .language-option:hover {
            color: white;
            transform: scale(1.05);
            text-decoration: none;
        }

        .language-option.active {
            background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 112, 243, 0.4);
            transform: scale(1.02);
        }

        .language-option.active:hover {
            transform: scale(1.08);
            box-shadow: 0 6px 20px rgba(0, 112, 243, 0.5);
        }

        .language-flag {
            font-size: 18px;
            margin-{% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 8px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .language-text {
            font-family: {% if request.session.django_language == 'ar' %}'Cairo', sans-serif{% else %}'Poppins', sans-serif{% endif %};
            letter-spacing: 0.5px;
        }

        /* Responsive design for mobile */
        @media (max-width: 768px) {
            .language-switcher {
                top: 20px;
                {% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 20px;
            }

            .language-toggle-container {
                padding: 6px;
            }

            .language-option {
                padding: 8px 12px;
                font-size: 13px;
                min-width: 70px;
            }

            .language-flag {
                font-size: 16px;
                margin-{% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 6px;
            }
        }

        /* Animation for language switch */
        .language-switch-animation {
            animation: languageSwitch 0.6s ease-in-out;
        }

        @keyframes languageSwitch {
            0% { opacity: 1; transform: translateY(0); }
            50% { opacity: 0.7; transform: translateY(-10px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .text-center {
            text-align: center !important;
        }

        .register-header {
            color: #fff;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .register-wrapper {
            display: flex;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        .register-left {
            flex: 1;
            background-color: #0a192f;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .register-right {
            flex: 1;
            background-color: #0a192f;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .register-form-container {
            width: 100%;
            max-width: 400px;
            padding: 2rem;
            background: rgba(10, 25, 47, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(100, 255, 255, 0.1);
            z-index: 10;
        }

        .form-floating {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .form-floating input {
            height: 60px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 1.5rem 1rem 0.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-floating input:focus {
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 0 0.25rem rgba(0, 112, 243, 0.25);
            border-color: rgba(0, 112, 243, 0.5);
        }

        .form-floating label {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            padding: 1rem 1rem;
            pointer-events: none;
            border: 1px solid transparent;
            transform-origin: 0 0;
            transition: opacity .1s ease-in-out,transform .1s ease-in-out;
            color: rgba(255, 255, 255, 0.6);
        }

        .form-floating input:focus ~ label,
        .form-floating input:not(:placeholder-shown) ~ label {
            opacity: .65;
            transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
        }

        .form-floating input::placeholder {
            color: transparent;
        }

        .btn-register {
            background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 0.75rem;
            border-radius: 15px;
            width: 100%;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 112, 243, 0.4);
        }

        .btn-register:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(0, 112, 243, 0.6);
        }

        .btn-register:active {
            transform: translateY(-1px);
        }

        .register-links {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
        }

        .register-links a {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .register-links a:hover {
            color: #00c6ff;
            text-shadow: 0 0 5px rgba(0, 198, 255, 0.3);
        }

        /* Input icons */
        .input-icon {
            position: absolute;
            top: 50%;
            {% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 1rem;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.4);
            font-size: 1.2rem;
            z-index: 10;
        }

        .form-floating input {
            padding-{% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 3rem;
        }

        /* Auto parts showcase */
        .parts-showcase {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            z-index: 1;
        }

        .part-item {
            position: absolute;
            width: 150px;
            height: 150px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.5));
            opacity: 0.8;
            transition: all 0.5s ease;
            transform: scale(0.8);
            animation: float-part 15s infinite ease-in-out;
        }

        .part-item:hover {
            transform: scale(1.1) !important;
            opacity: 1 !important;
            z-index: 10 !important;
        }

        @keyframes float-part {
            0% { transform: translateY(0) scale(0.8) rotate(0deg); }
            50% { transform: translateY(-20px) scale(0.85) rotate(5deg); }
            100% { transform: translateY(0) scale(0.8) rotate(0deg); }
        }

        .part-1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
            background-image: url('https://cdn.pixabay.com/photo/2013/07/12/14/53/car-148539_1280.png');
        }

        .part-2 {
            top: 60%;
            left: 20%;
            animation-delay: 2s;
            background-image: url('https://cdn.pixabay.com/photo/2014/04/02/10/39/disc-brake-304538_1280.png');
        }

        .part-3 {
            top: 30%;
            right: 15%;
            animation-delay: 1s;
            background-image: url('https://cdn.pixabay.com/photo/2013/07/13/10/21/car-157752_1280.png');
        }

        .part-4 {
            bottom: 20%;
            right: 25%;
            animation-delay: 3s;
            background-image: url('https://cdn.pixabay.com/photo/2013/07/12/14/53/car-148539_1280.png');
        }

        .part-5 {
            top: 50%;
            left: 5%;
            animation-delay: 4s;
            background-image: url('https://cdn.pixabay.com/photo/2014/04/02/10/39/disc-brake-304538_1280.png');
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .register-wrapper {
                flex-direction: column;
            }

            .register-left, .register-right {
                flex: none;
                height: 50vh;
            }

            .register-form-container {
                max-width: 350px;
                padding: 1.5rem;
            }

            .part-item {
                width: 100px;
                height: 100px;
            }
        }
    </style>
</head>

<body>
    <!-- Enhanced Language Switcher -->
    <div class="language-switcher">
        <div class="language-toggle-container">
            <a href="{% url 'set_language' %}?language=en&next={{ request.get_full_path }}"
               class="language-option {% if request.session.django_language == 'en' or not request.session.django_language %}active{% endif %}">
                <span class="language-flag">🇺🇸</span>
                <span class="language-text">English</span>
            </a>
            <a href="{% url 'set_language' %}?language=ar&next={{ request.get_full_path }}"
               class="language-option {% if request.session.django_language == 'ar' %}active{% endif %}">
                <span class="language-flag">🇸🇦</span>
                <span class="language-text">العربية</span>
            </a>
        </div>
    </div>

    <!-- Main Register Wrapper -->
    <div class="register-wrapper">
        <!-- Left Side - Auto Parts Showcase -->
        <div class="register-left">
            <div class="parts-showcase">
                <div class="part-item part-1"></div>
                <div class="part-item part-2"></div>
                <div class="part-item part-3"></div>
                <div class="part-item part-4"></div>
                <div class="part-item part-5"></div>
            </div>

            <div class="text-center" style="z-index: 20; position: relative;">
                <div class="mb-4">
                    <i class="fas fa-car fa-5x mb-4" style="color: #00c6ff; filter: drop-shadow(0 0 20px rgba(0, 198, 255, 0.5));"></i>
                    <h1 class="register-header display-4">{% custom_trans "Auto Parts Management" %}</h1>
                    <p class="lead" style="color: rgba(255, 255, 255, 0.8); font-size: 1.2rem;">
                        {% custom_trans "Join our platform to manage your auto parts business efficiently" %}
                    </p>
                </div>

                <div class="row text-center mt-5">
                    <div class="col-4">
                        <div class="feature-item">
                            <i class="fas fa-box fa-3x mb-3" style="color: #0070f3;"></i>
                            <h5 style="color: white;">{% custom_trans "Inventory" %}</h5>
                            <p style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">{% custom_trans "Manage your stock" %}</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="feature-item">
                            <i class="fas fa-chart-line fa-3x mb-3" style="color: #00c6ff;"></i>
                            <h5 style="color: white;">{% custom_trans "Analytics" %}</h5>
                            <p style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">{% custom_trans "Track performance" %}</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="feature-item">
                            <i class="fas fa-users fa-3x mb-3" style="color: #0070f3;"></i>
                            <h5 style="color: white;">{% custom_trans "Customers" %}</h5>
                            <p style="color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">{% custom_trans "Manage relationships" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Register Form -->
        <div class="register-right">
            <div class="register-form-container">
                <div class="text-center mb-4">
                    <h2 class="register-header h3">
                        <i class="fas fa-user-plus fa-fw mr-2"></i>
                        {% custom_trans "Create Account" %}
                    </h2>
                    <p style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Start managing your auto parts business today" %}</p>
                </div>

                {% if messages %}
                <div class="mb-4">
                    {% for message in messages %}
                    <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible fade show" role="alert" style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); color: #ff6b6b;">
                        <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} fa-fw mr-2"></i>
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close" style="color: #ff6b6b;">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <form method="post" action="{% url 'register_view' %}" id="registerForm">
                    {% csrf_token %}

                    <!-- Username Field -->
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username"
                               placeholder="{% custom_trans 'Username' %}" required autocomplete="username">
                        <label for="username">{% custom_trans "Username" %}</label>
                        <i class="fas fa-user input-icon"></i>
                    </div>

                    <!-- Password Field -->
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="{% custom_trans 'Password' %}" required autocomplete="new-password">
                        <label for="password">{% custom_trans "Password" %}</label>
                        <i class="fas fa-lock input-icon"></i>
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password2" name="password2"
                               placeholder="{% custom_trans 'Confirm Password' %}" required autocomplete="new-password">
                        <label for="password2">{% custom_trans "Confirm Password" %}</label>
                        <i class="fas fa-lock input-icon"></i>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-register" id="registerBtn">
                        <i class="fas fa-user-plus fa-fw mr-2"></i>
                        {% custom_trans "Create Account" %}
                    </button>
                </form>

                <!-- Links -->
                <div class="register-links">
                    <a href="{% url 'login_view' %}">
                        <i class="fas fa-sign-in-alt fa-fw mr-1"></i>
                        {% custom_trans "Already have an account? Sign In!" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{% static 'sbadmin/vendor/jquery/jquery.min.js' %}"></script>
    <script src="{% static 'sbadmin/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{% static 'sbadmin/vendor/jquery-easing/jquery.easing.min.js' %}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{% static 'sbadmin/js/sb-admin-2.min.js' %}"></script>

    <script>
        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            const submitBtn = document.getElementById('registerBtn');
            const password = document.getElementById('password');
            const password2 = document.getElementById('password2');

            // Password confirmation validation
            function validatePasswords() {
                if (password.value !== password2.value && password2.value !== '') {
                    password2.setCustomValidity('{% custom_trans "Passwords do not match" %}');
                    password2.style.borderColor = 'rgba(255, 107, 107, 0.5)';
                    password2.style.boxShadow = '0 0 0 0.25rem rgba(255, 107, 107, 0.25)';
                } else {
                    password2.setCustomValidity('');
                    password2.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    password2.style.boxShadow = 'none';
                }
            }

            password.addEventListener('input', validatePasswords);
            password2.addEventListener('input', validatePasswords);

            // Form submission enhancement
            form.addEventListener('submit', function(e) {
                validatePasswords();

                if (form.checkValidity()) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin fa-fw mr-2"></i>{% custom_trans "Creating Account..." %}';
                    submitBtn.disabled = true;
                    submitBtn.style.background = 'linear-gradient(135deg, #666 0%, #999 100%)';
                }
            });

            // Enhanced input focus effects
            const inputs = document.querySelectorAll('.form-floating input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                    this.parentElement.style.transition = 'all 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // Auto parts animation enhancement
            const parts = document.querySelectorAll('.part-item');
            parts.forEach((part, index) => {
                part.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2) rotate(10deg)';
                    this.style.zIndex = '100';
                    this.style.filter = 'drop-shadow(0 10px 30px rgba(0, 198, 255, 0.6))';
                });

                part.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(0.8) rotate(0deg)';
                    this.style.zIndex = '1';
                    this.style.filter = 'drop-shadow(0 5px 15px rgba(0, 0, 0, 0.5))';
                });
            });
        });

        // Language switcher animation
        function addSwitchAnimation() {
            const container = document.querySelector('.language-toggle-container');
            container.style.transform = 'scale(0.95)';
            container.style.transition = 'all 0.2s ease';
            setTimeout(() => {
                container.style.transform = 'scale(1)';
            }, 150);
        }

        // Add click handlers to language options
        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', function(e) {
                addSwitchAnimation();
                // Add language switch animation to body
                document.body.classList.add('language-switch-animation');
                setTimeout(() => {
                    document.body.classList.remove('language-switch-animation');
                }, 600);
            });
        });

        // Add subtle parallax effect to auto parts
        document.addEventListener('mousemove', function(e) {
            const parts = document.querySelectorAll('.part-item');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;

            parts.forEach((part, index) => {
                const speed = (index + 1) * 0.5;
                const x = (mouseX - 0.5) * speed;
                const y = (mouseY - 0.5) * speed;

                part.style.transform += ` translate(${x}px, ${y}px)`;
            });
        });
    </script>
</body>

</html>
