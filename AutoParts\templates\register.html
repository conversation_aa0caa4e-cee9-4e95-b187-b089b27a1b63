{% load static %}
{% load custom_i18n %}
<!DOCTYPE html>
<html lang="{% if request.session.django_language == 'ar' %}ar{% else %}en{% endif %}" dir="{% if request.session.django_language == 'ar' %}rtl{% else %}ltr{% endif %}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="{% custom_trans 'Auto Parts Management System' %}">
    <meta name="author" content="">

    <title>{% custom_trans 'Auto Parts Management System' %} - {% custom_trans 'Create Account' %}</title>

    <!-- Custom fonts for this template-->
    <link href="{% static 'sbadmin/vendor/fontawesome-free/css/all.min.css' %}" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;900&display=swap" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="{% static 'sbadmin/css/sb-admin-2.min.css' %}" rel="stylesheet">
    <!-- Custom styles for Auto Parts Management -->
    <link href="{% static 'custom.css' %}" rel="stylesheet">

    <style>
        body {
            font-family: {% if request.session.django_language == 'ar' %}'Cairo', sans-serif{% else %}'Poppins', sans-serif{% endif %} !important;
            direction: {% if request.session.django_language == 'ar' %}rtl{% else %}ltr{% endif %};
            margin: 0;
            overflow-x: hidden;
            background-color: #0a192f;
            height: 100vh;
        }

        .form-control {
            text-align: {% if request.session.django_language == 'ar' %}right{% else %}left{% endif %};
        }

        .btn {
            font-family: {% if request.session.django_language == 'ar' %}'Cairo', sans-serif{% else %}'Poppins', sans-serif{% endif %};
        }

        /* Enhanced Language switcher styles */
        .language-switcher {
            position: absolute;
            top: 30px;
            {% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 30px;
            z-index: 1000;
        }

        .language-toggle-container {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .language-option {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            color: #5a5c69;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .language-option.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .language-flag {
            margin-{% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 8px;
            font-size: 1.1rem;
        }

        /* Animated background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(-45deg, #0a192f, #112240, #1e3a8a, #3730a3);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            z-index: -2;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Floating particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Main container styles */
        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .register-image {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9)), url('{% static "service.jpg" %}');
            background-size: cover;
            background-position: center;
            position: relative;
            min-height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .register-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
        }

        .register-image-content {
            position: relative;
            z-index: 2;
            text-align: center;
            padding: 40px;
        }

        .register-form {
            padding: 60px 40px;
        }

        .form-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 30px;
            position: relative;
        }

        .form-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .form-control-user {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control-user:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .btn-user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-user:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-user::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-user:hover::before {
            left: 100%;
        }

        .form-links {
            margin-top: 30px;
        }

        .form-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .form-links a:hover {
            color: #764ba2;
            text-decoration: none;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .register-form {
                padding: 40px 30px;
            }

            .register-image {
                min-height: 300px;
            }

            .language-switcher {
                top: 15px;
                {% if request.session.django_language == 'ar' %}left{% else %}right{% endif %}: 15px;
            }
        }

        /* Input icons */
        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            {% if request.session.django_language == 'ar' %}right{% else %}left{% endif %}: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            z-index: 10;
        }

        .form-control-user.with-icon {
            padding-{% if request.session.django_language == 'ar' %}right{% else %}left{% endif %}: 45px;
        }
    </style>
</head>

<body>
    <!-- Animated Background -->
    <div class="animated-bg"></div>

    <!-- Floating Particles -->
    <div class="particles" id="particles"></div>

    <!-- Enhanced Language Switcher -->
    <div class="language-switcher">
        <div class="language-toggle-container">
            <a href="{% url 'set_language' %}?language=en&next={{ request.get_full_path }}"
               class="language-option {% if request.session.django_language == 'en' or not request.session.django_language %}active{% endif %}">
                <span class="language-flag">🇺🇸</span>
                <span class="language-text">English</span>
            </a>
            <a href="{% url 'set_language' %}?language=ar&next={{ request.get_full_path }}"
               class="language-option {% if request.session.django_language == 'ar' %}active{% endif %}">
                <span class="language-flag">🇸🇦</span>
                <span class="language-text">العربية</span>
            </a>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <div class="register-card">
            <div class="row no-gutters">
                <!-- Left Side - Image -->
                <div class="col-lg-6 d-none d-lg-block">
                    <div class="register-image">
                        <div class="register-image-content">
                            <div class="mb-4">
                                <i class="fas fa-car fa-4x mb-3" style="opacity: 0.9;"></i>
                                <h2 class="font-weight-bold">{% custom_trans "Auto Parts Management" %}</h2>
                                <p class="lead">{% custom_trans "Join our platform to manage your auto parts business efficiently" %}</p>
                            </div>
                            <div class="row text-center">
                                <div class="col-4">
                                    <i class="fas fa-box fa-2x mb-2"></i>
                                    <p class="small">{% custom_trans "Inventory" %}</p>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    <p class="small">{% custom_trans "Analytics" %}</p>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <p class="small">{% custom_trans "Customers" %}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Form -->
                <div class="col-lg-6">
                    <div class="register-form">
                        <div class="text-center">
                            <h1 class="h3 form-title">
                                <i class="fas fa-user-plus fa-fw mr-2"></i>
                                {% custom_trans "Create Account" %}
                            </h1>
                            <p class="text-muted mb-4">{% custom_trans "Start managing your auto parts business today" %}</p>
                        </div>

                        {% if messages %}
                        <div class="mb-4">
                            {% for message in messages %}
                            <div class="alert alert-{% if message.tags == 'error' %}danger{% else %}{{ message.tags }}{% endif %} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} fa-fw mr-2"></i>
                                {{ message }}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <form class="user" method="post" action="{% url 'register_view' %}" id="registerForm">
                            {% csrf_token %}

                            <!-- Username Field -->
                            <div class="form-group">
                                <div class="input-group">
                                    <i class="fas fa-user input-icon"></i>
                                    <input type="text" class="form-control form-control-user with-icon"
                                           id="username" name="username"
                                           placeholder="{% custom_trans 'Enter Username...' %}"
                                           required autocomplete="username">
                                </div>
                            </div>

                            <!-- Password Fields -->
                            <div class="form-group row">
                                <div class="col-sm-6 mb-3 mb-sm-0">
                                    <div class="input-group">
                                        <i class="fas fa-lock input-icon"></i>
                                        <input type="password" class="form-control form-control-user with-icon"
                                               id="password" name="password"
                                               placeholder="{% custom_trans 'Password' %}"
                                               required autocomplete="new-password">
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="input-group">
                                        <i class="fas fa-lock input-icon"></i>
                                        <input type="password" class="form-control form-control-user with-icon"
                                               id="password2" name="password2"
                                               placeholder="{% custom_trans 'Confirm Password' %}"
                                               required autocomplete="new-password">
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" class="btn btn-user btn-block" id="registerBtn">
                                <i class="fas fa-user-plus fa-fw mr-2"></i>
                                {% custom_trans "Create Account" %}
                            </button>
                        </form>

                        <!-- Links -->
                        <div class="form-links text-center">
                            <div class="mb-2">
                                <a class="small" href="{% url 'login_view' %}">
                                    <i class="fas fa-sign-in-alt fa-fw mr-1"></i>
                                    {% custom_trans "Already have an account? Sign In!" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="{% static 'sbadmin/vendor/jquery/jquery.min.js' %}"></script>
    <script src="{% static 'sbadmin/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>

    <!-- Core plugin JavaScript-->
    <script src="{% static 'sbadmin/vendor/jquery-easing/jquery.easing.min.js' %}"></script>

    <!-- Custom scripts for all pages-->
    <script src="{% static 'sbadmin/js/sb-admin-2.min.js' %}"></script>

    <script>
        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 2px and 6px
                const size = Math.random() * 4 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';

                // Random position
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';

                // Random animation delay
                particle.style.animationDelay = Math.random() * 6 + 's';

                // Random animation duration
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';

                particlesContainer.appendChild(particle);
            }
        }

        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();

            const form = document.getElementById('registerForm');
            const submitBtn = document.getElementById('registerBtn');
            const password = document.getElementById('password');
            const password2 = document.getElementById('password2');

            // Password confirmation validation
            function validatePasswords() {
                if (password.value !== password2.value) {
                    password2.setCustomValidity('{% custom_trans "Passwords do not match" %}');
                    password2.classList.add('is-invalid');
                } else {
                    password2.setCustomValidity('');
                    password2.classList.remove('is-invalid');
                }
            }

            password.addEventListener('input', validatePasswords);
            password2.addEventListener('input', validatePasswords);

            // Form submission enhancement
            form.addEventListener('submit', function(e) {
                validatePasswords();

                if (form.checkValidity()) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin fa-fw mr-2"></i>{% custom_trans "Creating Account..." %}';
                    submitBtn.disabled = true;
                }
            });

            // Input focus effects
            const inputs = document.querySelectorAll('.form-control-user');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });
        });

        // Language switcher animation
        function addSwitchAnimation() {
            document.querySelector('.language-toggle-container').style.transform = 'scale(0.95)';
            setTimeout(() => {
                document.querySelector('.language-toggle-container').style.transform = 'scale(1)';
            }, 150);
        }

        // Add click handlers to language options
        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', addSwitchAnimation);
        });
    </script>
</body>

</html>
