{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Purchases" %} - Auto Parts Management{% endblock %}

{% block extra_css %}
<!-- Custom styles for this page -->
<link href="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.css' %}" rel="stylesheet">

<style>
    /* Enhanced page styling matching Products page */
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1.5rem !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .page-heading::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 112, 243, 0.05), transparent);
        animation: page-shine 4s infinite;
    }

    @keyframes page-shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .btn-add-purchase {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.75rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        color: white !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.3) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .btn-add-purchase::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-add-purchase:hover::before {
        left: 100%;
    }

    .btn-add-purchase:hover {
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 10px 30px rgba(0, 112, 243, 0.5) !important;
        color: white !important;
    }

    /* Enhanced table styling */
    .table-container {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        overflow: hidden !important;
    }

    .filter-container {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        margin-bottom: 2rem !important;
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-radius: 0.5rem !important;
    }

    /* Modal enhancements - Complete fix for interaction issues */
    .modal {
        z-index: 9999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: auto !important;
        background-color: rgba(10, 25, 47, 0.8) !important;
    }

    .modal-backdrop {
        display: none !important;
    }

    .modal-dialog {
        z-index: 10000 !important;
        position: relative !important;
        margin: 50px auto !important;
        pointer-events: auto !important;
    }

    .modal-content {
        z-index: 10001 !important;
        position: relative !important;
        pointer-events: auto !important;
        margin: 0 auto !important;
    }

    .modal-content .form-control {
        pointer-events: auto !important;
        z-index: 1080 !important;
        position: relative !important;
    }

    .modal-content .form-control:focus {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: #0070f3 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 112, 243, 0.25) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        z-index: 1090 !important;
    }

    .modal-content .form-control::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
    }

    /* Force interaction on all modal elements */
    .modal input,
    .modal textarea,
    .modal select,
    .modal button {
        pointer-events: auto !important;
        z-index: 1060 !important;
        position: relative !important;
        -webkit-user-select: auto !important;
        -moz-user-select: auto !important;
        -ms-user-select: auto !important;
        user-select: auto !important;
    }

    /* Disable any overlay that might interfere */
    .modal-dialog {
        pointer-events: auto !important;
    }

    .modal-content {
        pointer-events: auto !important;
    }

    /* Ensure no pseudo-elements block interaction */
    .modal *::before,
    .modal *::after {
        pointer-events: none !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <div class="d-sm-flex align-items-center justify-content-between">
        <div>
            <h1 class="h3 mb-0" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important;">
                <i class="fas fa-shopping-cart fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important; animation: pulse-icon 2s infinite ease-in-out;"></i>
                {% custom_trans "Purchases" %}
            </h1>
            <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7); font-size: 1rem;">
                {% custom_trans "Manage your purchase orders and transactions here" %}
            </p>
        </div>
        <button type="button" class="btn btn-add-purchase" onclick="openAddModal()">
            <i class="fas fa-plus-circle fa-sm fa-fw mr-2"></i>{% custom_trans "Add Purchase" %}
        </button>
    </div>
</div>

<!-- Filter Form -->
<div class="card filter-container shadow mb-4">
    <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(0, 112, 243, 0.2); box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1);">
        <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            <i class="fas fa-filter fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: pulse-icon 2s infinite ease-in-out;"></i>
            {% custom_trans "Filter Purchases" %}
        </h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases_view' %}" method="get">
            <div class="form-row">
                <div class="form-group col-md-4">
                    <label for="purchaser" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-user fa-fw mr-1"></i>{% custom_trans "Purchaser" %}
                    </label>
                    <select class="form-control" id="purchaser" name="purchaser"
                            style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        <option value="">{% custom_trans "All Purchasers" %}</option>
                        {% for purchaser in purchasers %}
                        <option value="{{ purchaser.id }}" {% if selected_purchaser == purchaser.id|stringformat:"s" %}selected{% endif %}>{{ purchaser.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group col-md-4">
                    <label for="product" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-box fa-fw mr-1"></i>{% custom_trans "Product" %}
                    </label>
                    <select class="form-control" id="product" name="product"
                            style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        <option value="">{% custom_trans "All Products" %}</option>
                        {% for product in products %}
                        <option value="{{ product.id }}" {% if selected_product == product.id|stringformat:"s" %}selected{% endif %}>{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group col-md-4">
                    <label for="date" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-calendar-alt fa-fw mr-1"></i>{% custom_trans "Date" %}
                    </label>
                    <input type="date" class="form-control" id="date" name="date" value="{{ selected_date }}"
                           style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                </div>
            </div>
            <div class="text-center">
                <button type="submit" class="btn btn-primary mr-3"
                        style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border: none; border-radius: 0.5rem; font-weight: 600; padding: 0.5rem 1.5rem;">
                    <i class="fas fa-search fa-fw mr-1"></i>{% custom_trans "Filter" %}
                </button>
                <a href="{% url 'purchases_view' %}" class="btn btn-secondary"
                   style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white; padding: 0.5rem 1.5rem;">
                    <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Reset" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Purchases Table -->
<div class="card table-container shadow mb-4">
    <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(0, 112, 243, 0.2); box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1);">
        <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            <i class="fas fa-list fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: pulse-icon 2s infinite ease-in-out;"></i>
            {% custom_trans "Purchases List" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-hashtag fa-fw mr-1"></i>{% custom_trans "ID" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-user fa-fw mr-1"></i>{% custom_trans "Purchaser" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-box fa-fw mr-1"></i>{% custom_trans "Product" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-sort-numeric-up fa-fw mr-1"></i>{% custom_trans "Quantity" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-dollar-sign fa-fw mr-1"></i>{% custom_trans "Price" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-calculator fa-fw mr-1"></i>{% custom_trans "Total" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-credit-card fa-fw mr-1"></i>{% custom_trans "Debt" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-boxes fa-fw mr-1"></i>{% custom_trans "Bulk" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-calendar fa-fw mr-1"></i>{% custom_trans "Date" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-cogs fa-fw mr-1"></i>{% custom_trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "ID" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Purchaser" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Product" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Quantity" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Price" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Total" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Debt" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Bulk" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Date" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Actions" %}</th>
                    </tr>
                </tfoot>
                <tbody>
                    {% for purchase in purchases %}
                    <tr style="color: rgba(255, 255, 255, 0.8);">
                        <td>{{ purchase.id }}</td>
                        <td>
                            <i class="fas fa-user-circle fa-fw mr-2" style="color: #0070f3;"></i>
                            {{ purchase.purchaser.name }}
                        </td>
                        <td>
                            <i class="fas fa-cube fa-fw mr-2" style="color: #00c6ff;"></i>
                            {{ purchase.product.name }}
                        </td>
                        <td>
                            <i class="fas fa-sort-numeric-up fa-fw mr-2" style="color: #28a745;"></i>
                            {{ purchase.quantity }}
                        </td>
                        <td>
                            <i class="fas fa-dollar-sign fa-fw mr-2" style="color: #ffc107;"></i>
                            {% if purchase.bulk %}
                            ${{ purchase.product.bulk_price }}
                            {% else %}
                            ${{ purchase.product.price }}
                            {% endif %}
                        </td>
                        <td>
                            <i class="fas fa-calculator fa-fw mr-2" style="color: #17a2b8;"></i>
                            {% if purchase.bulk %}
                            ${{ purchase.product.bulk_price|multiply:purchase.quantity }}
                            {% else %}
                            ${{ purchase.product.price|multiply:purchase.quantity }}
                            {% endif %}
                        </td>
                        <td>
                            {% if purchase.debt %}
                            <span class="badge" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); color: white; border-radius: 0.5rem;">
                                <i class="fas fa-exclamation-triangle fa-fw mr-1"></i>{% custom_trans "Yes" %}
                            </span>
                            {% else %}
                            <span class="badge" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 0.5rem;">
                                <i class="fas fa-check-circle fa-fw mr-1"></i>{% custom_trans "No" %}
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if purchase.bulk %}
                            <span class="badge" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); color: white; border-radius: 0.5rem;">
                                <i class="fas fa-boxes fa-fw mr-1"></i>{% custom_trans "Yes" %}
                            </span>
                            {% else %}
                            <span class="badge" style="background: rgba(108, 117, 125, 0.8); color: white; border-radius: 0.5rem;">
                                <i class="fas fa-box fa-fw mr-1"></i>{% custom_trans "No" %}
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <i class="fas fa-calendar-alt fa-fw mr-2" style="color: #6f42c1;"></i>
                            {{ purchase.date|date:"M d, Y" }}
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm edit-purchase"
                                data-id="{{ purchase.id }}"
                                data-purchaser="{{ purchase.purchaser.id }}"
                                data-product="{{ purchase.product.id }}"
                                data-quantity="{{ purchase.quantity }}"
                                data-debt="{{ purchase.debt }}"
                                data-bulk="{{ purchase.bulk }}"
                                style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; border-radius: 0.5rem; color: white; margin-right: 0.5rem;">
                                <i class="fas fa-edit fa-fw"></i> {% custom_trans "Edit" %}
                            </button>
                            <button type="button" class="btn btn-sm delete-purchase" data-id="{{ purchase.id }}"
                                    style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); border: none; border-radius: 0.5rem; color: white;">
                                <i class="fas fa-trash fa-fw"></i> {% custom_trans "Delete" %}
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Purchase Modal -->
<div class="modal fade" id="editPurchaseModal" tabindex="-1" role="dialog" aria-labelledby="editPurchaseModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPurchaseModalLabel">Edit Purchase</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <form action="{% url 'purchases_edit' %}" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <input type="hidden" name="purchase_id" id="edit-purchase-id">
                    <div class="form-group">
                        <label for="edit-purchase-purchaser">Purchaser</label>
                        <select class="form-control" id="edit-purchase-purchaser" name="purchaser" required>
                            {% for purchaser in purchasers %}
                            <option value="{{ purchaser.id }}">{{ purchaser.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit-purchase-product">Product</label>
                        <select class="form-control" id="edit-purchase-product" name="product" required>
                            {% for product in products %}
                            <option value="{{ product.id }}">{{ product.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit-purchase-quantity">Quantity</label>
                        <input type="number" class="form-control" id="edit-purchase-quantity" name="quantity" min="1" required>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="edit-purchase-debt" name="debt" value="on">
                            <label class="custom-control-label" for="edit-purchase-debt">Debt</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="edit-purchase-bulk" name="bulk" value="on">
                            <label class="custom-control-label" for="edit-purchase-bulk">Bulk</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Purchase Modal -->
<div class="modal fade" id="addPurchaseModal" tabindex="-1" role="dialog" aria-labelledby="addPurchaseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="background: rgba(10, 25, 47, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 1rem; backdrop-filter: blur(20px);">
            <div class="modal-header" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border-radius: 1rem 1rem 0 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="addPurchaseModalLabel" style="color: white; font-weight: 600;">
                    <i class="fas fa-plus-circle fa-fw mr-2"></i>{% custom_trans "Add Purchase" %}
                </h5>
                <button class="close" type="button" onclick="closeAddModal()" aria-label="Close" style="color: white; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{% url 'purchases_add' %}" method="post">
                {% csrf_token %}
                <div class="modal-body" style="background: transparent; color: rgba(255, 255, 255, 0.9);">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="add-purchase-purchaser" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-user fa-fw mr-1"></i>{% custom_trans "Purchaser" %}
                            </label>
                            <select class="form-control" id="add-purchase-purchaser" name="purchaser" required
                                    style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                                <option value="">{% custom_trans "Select Purchaser" %}</option>
                                {% for purchaser in purchasers %}
                                <option value="{{ purchaser.id }}">{{ purchaser.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="add-purchase-product" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-box fa-fw mr-1"></i>{% custom_trans "Product" %}
                            </label>
                            <select class="form-control" id="add-purchase-product" name="product" required
                                    style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                                <option value="">{% custom_trans "Select Product" %}</option>
                                {% for product in products %}
                                <option value="{{ product.id }}" data-price="{{ product.price }}" data-bulk-price="{{ product.bulk_price }}">{{ product.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label for="add-purchase-quantity" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-sort-numeric-up fa-fw mr-1"></i>{% custom_trans "Quantity" %}
                            </label>
                            <input type="number" class="form-control" id="add-purchase-quantity" name="quantity" min="1" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                        <div class="form-group col-md-4">
                            <label for="add-purchase-price" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-dollar-sign fa-fw mr-1"></i>{% custom_trans "Unit Price" %}
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.7);">$</span>
                                </div>
                                <input type="text" class="form-control" id="add-purchase-price" readonly
                                       style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9);">
                            </div>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="add-purchase-total" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-calculator fa-fw mr-1"></i>{% custom_trans "Total" %}
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.7);">$</span>
                                </div>
                                <input type="text" class="form-control" id="add-purchase-total" readonly
                                       style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9);">
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="add-purchase-debt" name="debt" value="on">
                                <label class="custom-control-label" for="add-purchase-debt" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                    <i class="fas fa-credit-card fa-fw mr-1"></i>{% custom_trans "Debt" %}
                                </label>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="add-purchase-bulk" name="bulk" value="on">
                                <label class="custom-control-label" for="add-purchase-bulk" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                    <i class="fas fa-boxes fa-fw mr-1"></i>{% custom_trans "Bulk" %}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: transparent; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button class="btn btn-secondary" type="button" onclick="closeAddModal()"
                            style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white;">
                        <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Cancel" %}
                    </button>
                    <button type="submit" class="btn btn-primary"
                            style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border: none; border-radius: 0.5rem; font-weight: 600;">
                        <i class="fas fa-plus-circle fa-fw mr-1"></i>{% custom_trans "Add Purchase" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Purchase Modal -->
<div class="modal fade" id="deletePurchaseModal" tabindex="-1" role="dialog" aria-labelledby="deletePurchaseModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePurchaseModalLabel">Delete Purchase</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">Are you sure you want to delete this purchase? This action cannot be undone.</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                <form action="{% url 'purchases_remove' %}" method="post">
                    {% csrf_token %}
                    <input type="hidden" name="purchase_id" id="delete-purchase-id">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Page level plugins -->
<script src="{% static 'sbadmin/vendor/datatables/jquery.dataTables.min.js' %}"></script>
<script src="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.js' %}"></script>

<!-- Page level custom scripts -->
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#dataTable').DataTable({
            "order": [[0, "desc"]] // Sort by ID in descending order by default
        });

        // Edit Purchase
        $('.edit-purchase').click(function() {
            var id = $(this).data('id');
            var purchaser = $(this).data('purchaser');
            var product = $(this).data('product');
            var quantity = $(this).data('quantity');
            var debt = $(this).data('debt');
            var bulk = $(this).data('bulk');

            $('#edit-purchase-id').val(id);
            $('#edit-purchase-purchaser').val(purchaser);
            $('#edit-purchase-product').val(product);
            $('#edit-purchase-quantity').val(quantity);
            $('#edit-purchase-debt').prop('checked', debt === true);
            $('#edit-purchase-bulk').prop('checked', bulk === true);

            $('#editPurchaseModal').modal('show');
        });

        // Delete Purchase
        $('.delete-purchase').click(function() {
            var id = $(this).data('id');
            $('#delete-purchase-id').val(id);
            $('#deletePurchaseModal').modal('show');
        });

        // Add Purchase - Update price and total when product or bulk changes
        function updatePriceAndTotal() {
            var selectedOption = $('#add-purchase-product option:selected');
            var price = 0;

            if (selectedOption.val()) {
                if ($('#add-purchase-bulk').is(':checked')) {
                    price = parseFloat(selectedOption.data('bulk-price'));
                } else {
                    price = parseFloat(selectedOption.data('price'));
                }
            }

            $('#add-purchase-price').val(price.toFixed(2));

            var quantity = parseInt($('#add-purchase-quantity').val()) || 0;
            var total = price * quantity;
            $('#add-purchase-total').val(total.toFixed(2));
        }

        $('#add-purchase-product').change(updatePriceAndTotal);
        $('#add-purchase-bulk').change(updatePriceAndTotal);
        $('#add-purchase-quantity').on('input', updatePriceAndTotal);

        // Force remove any existing backdrops on page load
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });

    // Custom modal functions
    function openAddModal() {
        // Remove any existing backdrop
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Show modal with custom method
        $('#addPurchaseModal').css({
            'display': 'block',
            'z-index': '9999',
            'position': 'fixed',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(10, 25, 47, 0.8)',
            'overflow': 'auto'
        }).addClass('show');

        // Ensure all elements are interactive
        $('#addPurchaseModal input, #addPurchaseModal select, #addPurchaseModal button').css({
            'pointer-events': 'auto',
            'z-index': '10001'
        });

        // Focus first input
        setTimeout(function() {
            $('#add-purchase-purchaser').focus();
        }, 100);
    }

    function closeAddModal() {
        $('#addPurchaseModal').css('display', 'none').removeClass('show');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    }

    // Fix modal interaction issues
    $('#addPurchaseModal').on('click', function(e) {
        if (e.target === this) {
            closeAddModal();
        }
    });
</script>
{% endblock %}
