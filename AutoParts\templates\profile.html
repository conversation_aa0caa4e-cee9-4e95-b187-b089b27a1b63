{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "User Profile" %}{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-user fa-fw mr-2"></i>{% custom_trans "User Profile" %}
    </h1>
</div>

<!-- Profile Content -->
<div class="row">
    <!-- Profile Information Card -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user-circle fa-fw mr-2"></i>{% custom_trans "Profile Information" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        <div class="profile-avatar">
                            <i class="fas fa-user-circle fa-5x text-gray-400"></i>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% custom_trans "Username" %}:</strong></td>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% custom_trans "Email" %}:</strong></td>
                                <td>{{ user.email|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% custom_trans "First Name" %}:</strong></td>
                                <td>{{ user.first_name|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% custom_trans "Last Name" %}:</strong></td>
                                <td>{{ user.last_name|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% custom_trans "Date Joined" %}:</strong></td>
                                <td>{{ user.date_joined|date:"F d, Y" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% custom_trans "Last Login" %}:</strong></td>
                                <td>{{ user.last_login|date:"F d, Y H:i"|default:"Never" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-primary" data-toggle="modal" data-target="#editProfileModal">
                        <i class="fas fa-edit fa-fw mr-2"></i>{% custom_trans "Edit Profile" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Statistics Card -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-bar fa-fw mr-2"></i>{% custom_trans "Account Statistics" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            {% custom_trans "Total Products" %}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ products_count|default:0 }}
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-box fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            {% custom_trans "Total Purchases" %}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ purchases_count|default:0 }}
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Two-Factor Authentication Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Two-Factor Authentication" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3 text-center mb-3">
                        {% if has_2fa %}
                            <i class="fas fa-lock fa-4x text-success"></i>
                        {% else %}
                            <i class="fas fa-unlock fa-4x text-warning"></i>
                        {% endif %}
                    </div>
                    <div class="col-md-9">
                        <h5>
                            {% if has_2fa %}
                                <span class="badge badge-success">{% custom_trans "Enabled" %}</span>
                                {% custom_trans "Your account is protected with two-factor authentication" %}
                            {% else %}
                                <span class="badge badge-warning">{% custom_trans "Disabled" %}</span>
                                {% custom_trans "Your account is not protected with two-factor authentication" %}
                            {% endif %}
                        </h5>
                        <p class="text-muted">
                            {% custom_trans "Two-factor authentication adds an extra layer of security to your account by requiring a verification code in addition to your password." %}
                        </p>
                        <div class="mt-3">
                            {% if has_2fa %}
                                <a href="{% url 'two_factor:backup_tokens' %}" class="btn btn-primary mr-2">
                                    <i class="fas fa-key fa-fw mr-2"></i>{% custom_trans "Manage Backup Tokens" %}
                                </a>
                                <a href="{% url 'two_factor:disable' %}" class="btn btn-warning">
                                    <i class="fas fa-times fa-fw mr-2"></i>{% custom_trans "Disable 2FA" %}
                                </a>
                            {% else %}
                                <a href="{% url 'two_factor:setup' %}" class="btn btn-success">
                                    <i class="fas fa-qrcode fa-fw mr-2"></i>{% custom_trans "Enable Two-Factor Authentication" %}
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1" role="dialog" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProfileModalLabel">
                    <i class="fas fa-edit fa-fw mr-2"></i>{% custom_trans "Edit Profile" %}
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group">
                        <label for="first_name">{% custom_trans "First Name" %}</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user.first_name }}">
                    </div>
                    <div class="form-group">
                        <label for="last_name">{% custom_trans "Last Name" %}</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user.last_name }}">
                    </div>
                    <div class="form-group">
                        <label for="email">{% custom_trans "Email" %}</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times fa-fw mr-2"></i>{% custom_trans "Cancel" %}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save fa-fw mr-2"></i>{% custom_trans "Save Changes" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
