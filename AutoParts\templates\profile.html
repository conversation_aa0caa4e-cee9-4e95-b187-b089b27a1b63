{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "User Profile" %}{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced page styling matching other pages */
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1.5rem !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .page-heading::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 112, 243, 0.05), transparent);
        animation: page-shine 4s infinite;
    }

    @keyframes page-shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* Enhanced card styling */
    .profile-card {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        overflow: hidden !important;
    }

    .profile-avatar {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
        border-radius: 50%;
        width: 120px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        box-shadow: 0 10px 30px rgba(0, 112, 243, 0.3);
    }

    .profile-avatar i {
        color: white !important;
        font-size: 4rem !important;
    }

    .profile-info-table {
        color: rgba(255, 255, 255, 0.9);
    }

    .profile-info-table td {
        padding: 0.75rem 0;
        border: none;
    }

    .profile-info-table strong {
        color: rgba(255, 255, 255, 0.8);
    }

    .btn-edit-profile {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.75rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        color: white !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.3) !important;
    }

    .btn-edit-profile:hover {
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 10px 30px rgba(0, 112, 243, 0.5) !important;
        color: white !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <div class="d-sm-flex align-items-center justify-content-between">
        <div>
            <h1 class="h3 mb-0" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important;">
                <i class="fas fa-user fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important; animation: pulse-icon 2s infinite ease-in-out;"></i>
                {% custom_trans "User Profile" %}
            </h1>
            <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7); font-size: 1rem;">
                {% custom_trans "Manage your account information and settings" %}
            </p>
        </div>
    </div>
</div>

<!-- Profile Content -->
<div class="row">
    <!-- Profile Information Card -->
    <div class="col-lg-6">
        <div class="card profile-card shadow mb-4">
            <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(0, 112, 243, 0.2); box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1);">
                <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <i class="fas fa-user-circle fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: pulse-icon 2s infinite ease-in-out;"></i>
                    {% custom_trans "Profile Information" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        <div class="profile-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <table class="table table-borderless profile-info-table">
                            <tr>
                                <td><strong><i class="fas fa-user fa-fw mr-2" style="color: #0070f3;"></i>{% custom_trans "Username" %}:</strong></td>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <td><strong><i class="fas fa-envelope fa-fw mr-2" style="color: #00c6ff;"></i>{% custom_trans "Email" %}:</strong></td>
                                <td>{{ user.email|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong><i class="fas fa-id-card fa-fw mr-2" style="color: #28a745;"></i>{% custom_trans "First Name" %}:</strong></td>
                                <td>{{ user.first_name|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong><i class="fas fa-id-badge fa-fw mr-2" style="color: #ffc107;"></i>{% custom_trans "Last Name" %}:</strong></td>
                                <td>{{ user.last_name|default:"Not provided" }}</td>
                            </tr>
                            <tr>
                                <td><strong><i class="fas fa-calendar-plus fa-fw mr-2" style="color: #17a2b8;"></i>{% custom_trans "Date Joined" %}:</strong></td>
                                <td>{{ user.date_joined|date:"F d, Y" }}</td>
                            </tr>
                            <tr>
                                <td><strong><i class="fas fa-clock fa-fw mr-2" style="color: #6f42c1;"></i>{% custom_trans "Last Login" %}:</strong></td>
                                <td>{{ user.last_login|date:"F d, Y H:i"|default:"Never" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-edit-profile" data-toggle="modal" data-target="#editProfileModal">
                        <i class="fas fa-edit fa-fw mr-2"></i>{% custom_trans "Edit Profile" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Statistics Card -->
    <div class="col-lg-6">
        <div class="card profile-card shadow mb-4">
            <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(0, 112, 243, 0.2); box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1);">
                <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <i class="fas fa-chart-bar fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: pulse-icon 2s infinite ease-in-out;"></i>
                    {% custom_trans "Account Statistics" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            {% custom_trans "Total Products" %}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ products_count|default:0 }}
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-box fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            {% custom_trans "Total Purchases" %}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ purchases_count|default:0 }}
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Two-Factor Authentication Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Two-Factor Authentication" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3 text-center mb-3">
                        {% if has_2fa %}
                            <i class="fas fa-lock fa-4x text-success"></i>
                        {% else %}
                            <i class="fas fa-unlock fa-4x text-warning"></i>
                        {% endif %}
                    </div>
                    <div class="col-md-9">
                        <h5>
                            {% if has_2fa %}
                                <span class="badge badge-success">{% custom_trans "Enabled" %}</span>
                                {% custom_trans "Your account is protected with two-factor authentication" %}
                            {% else %}
                                <span class="badge badge-warning">{% custom_trans "Disabled" %}</span>
                                {% custom_trans "Your account is not protected with two-factor authentication" %}
                            {% endif %}
                        </h5>
                        <p class="text-muted">
                            {% custom_trans "Two-factor authentication adds an extra layer of security to your account by requiring a verification code in addition to your password." %}
                        </p>
                        <div class="mt-3">
                            {% if has_2fa %}
                                <a href="{% url 'two_factor:backup_tokens' %}" class="btn btn-primary mr-2">
                                    <i class="fas fa-key fa-fw mr-2"></i>{% custom_trans "Manage Backup Tokens" %}
                                </a>
                                <a href="{% url 'two_factor:disable' %}" class="btn btn-warning">
                                    <i class="fas fa-times fa-fw mr-2"></i>{% custom_trans "Disable 2FA" %}
                                </a>
                            {% else %}
                                <a href="{% url 'two_factor:setup' %}" class="btn btn-success">
                                    <i class="fas fa-qrcode fa-fw mr-2"></i>{% custom_trans "Enable Two-Factor Authentication" %}
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1" role="dialog" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProfileModalLabel">
                    <i class="fas fa-edit fa-fw mr-2"></i>{% custom_trans "Edit Profile" %}
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group">
                        <label for="first_name">{% custom_trans "First Name" %}</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user.first_name }}">
                    </div>
                    <div class="form-group">
                        <label for="last_name">{% custom_trans "Last Name" %}</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user.last_name }}">
                    </div>
                    <div class="form-group">
                        <label for="email">{% custom_trans "Email" %}</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times fa-fw mr-2"></i>{% custom_trans "Cancel" %}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save fa-fw mr-2"></i>{% custom_trans "Save Changes" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
