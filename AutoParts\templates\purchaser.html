{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Purchasers" %} - Auto Parts Management{% endblock %}

{% block extra_css %}
<!-- Custom styles for this page -->
<link href="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.css' %}" rel="stylesheet">

<style>
    /* Enhanced page styling matching other pages */
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1.5rem !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .page-heading::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 112, 243, 0.05), transparent);
        animation: page-shine 4s infinite;
    }

    @keyframes page-shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .btn-add-purchaser {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.75rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        color: white !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.3) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .btn-add-purchaser::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-add-purchaser:hover::before {
        left: 100%;
    }

    .btn-add-purchaser:hover {
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 10px 30px rgba(0, 112, 243, 0.5) !important;
        color: white !important;
    }

    /* Enhanced table styling */
    .table-container {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        overflow: hidden !important;
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-radius: 0.5rem !important;
    }

    /* Modal enhancements */
    .modal {
        z-index: 9999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: auto !important;
        background-color: rgba(10, 25, 47, 0.8) !important;
    }

    .modal-backdrop {
        display: none !important;
    }

    .modal-dialog {
        z-index: 10000 !important;
        position: relative !important;
        margin: 50px auto !important;
        pointer-events: auto !important;
    }

    .modal-content {
        z-index: 10001 !important;
        position: relative !important;
        pointer-events: auto !important;
        margin: 0 auto !important;
    }

    .modal input,
    .modal textarea,
    .modal select,
    .modal button {
        pointer-events: auto !important;
        z-index: 1060 !important;
        position: relative !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <div class="d-sm-flex align-items-center justify-content-between">
        <div>
            <h1 class="h3 mb-0" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important;">
                <i class="fas fa-users fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important; animation: pulse-icon 2s infinite ease-in-out;"></i>
                {% custom_trans "Purchasers" %}
            </h1>
            <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7); font-size: 1rem;">
                {% custom_trans "Manage your customers and buyers here" %}
            </p>
        </div>
        <button type="button" class="btn btn-add-purchaser" onclick="openAddModal()">
            <i class="fas fa-plus-circle fa-sm fa-fw mr-2"></i>{% custom_trans "Add Purchaser" %}
        </button>
    </div>
</div>

<!-- Purchasers Table -->
<div class="card table-container shadow mb-4">
    <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(0, 112, 243, 0.2); box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1);">
        <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            <i class="fas fa-list fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: pulse-icon 2s infinite ease-in-out;"></i>
            {% custom_trans "Purchasers List" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-hashtag fa-fw mr-1"></i>{% custom_trans "ID" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-user fa-fw mr-1"></i>{% custom_trans "Name" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-phone fa-fw mr-1"></i>{% custom_trans "Phone" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-dollar-sign fa-fw mr-1"></i>{% custom_trans "Amount" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-star fa-fw mr-1"></i>{% custom_trans "Special Client" %}
                        </th>
                        <th style="color: rgba(255, 255, 255, 0.9);">
                            <i class="fas fa-cogs fa-fw mr-1"></i>{% custom_trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "ID" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Name" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Phone" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Amount" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Special Client" %}</th>
                        <th style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Actions" %}</th>
                    </tr>
                </tfoot>
                <tbody>
                    {% for purchaser in purchasers %}
                    <tr style="color: rgba(255, 255, 255, 0.8);">
                        <td>{{ purchaser.id }}</td>
                        <td>
                            <i class="fas fa-user-circle fa-fw mr-2" style="color: #0070f3;"></i>
                            {{ purchaser.name }}
                        </td>
                        <td>
                            <i class="fas fa-phone-alt fa-fw mr-2" style="color: #00c6ff;"></i>
                            {{ purchaser.phone }}
                        </td>
                        <td>
                            <i class="fas fa-dollar-sign fa-fw mr-2" style="color: #28a745;"></i>
                            ${{ purchaser.amount }}
                        </td>
                        <td>
                            {% if purchaser.special_client %}
                            <span class="badge" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white; border-radius: 0.5rem;">
                                <i class="fas fa-star fa-fw mr-1"></i>{% custom_trans "Yes" %}
                            </span>
                            {% else %}
                            <span class="badge" style="background: rgba(108, 117, 125, 0.8); color: white; border-radius: 0.5rem;">
                                <i class="fas fa-user fa-fw mr-1"></i>{% custom_trans "No" %}
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm edit-purchaser"
                                data-id="{{ purchaser.id }}"
                                data-name="{{ purchaser.name }}"
                                data-phone="{{ purchaser.phone }}"
                                data-amount="{{ purchaser.amount }}"
                                data-special="{{ purchaser.special_client }}"
                                style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; border-radius: 0.5rem; color: white; margin-right: 0.5rem;">
                                <i class="fas fa-edit fa-fw"></i> {% custom_trans "Edit" %}
                            </button>
                            <button type="button" class="btn btn-sm delete-purchaser" data-id="{{ purchaser.id }}"
                                    style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); border: none; border-radius: 0.5rem; color: white;">
                                <i class="fas fa-trash fa-fw"></i> {% custom_trans "Delete" %}
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Purchaser Modal -->
<div class="modal fade" id="editPurchaserModal" tabindex="-1" role="dialog" aria-labelledby="editPurchaserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPurchaserModalLabel">Edit Purchaser</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <form action="{% url 'purchaser_edit' %}" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <input type="hidden" name="id" id="edit-purchaser-id">
                    <div class="form-group">
                        <label for="edit-purchaser-name">Name</label>
                        <input type="text" class="form-control" id="edit-purchaser-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-purchaser-phone">Phone</label>
                        <input type="text" class="form-control" id="edit-purchaser-phone" name="phone" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-purchaser-amount">Amount</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" step="0.01" class="form-control" id="edit-purchaser-amount" name="amount" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="edit-purchaser-special" name="special_client">
                            <label class="custom-control-label" for="edit-purchaser-special">Special Client</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Purchaser Modal -->
<div class="modal fade" id="addPurchaserModal" tabindex="-1" role="dialog" aria-labelledby="addPurchaserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPurchaserModalLabel">Add Purchaser</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <form action="{% url 'purchaser_add' %}" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="add-purchaser-name">Name</label>
                            <input type="text" class="form-control" id="add-purchaser-name" name="name" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="add-purchaser-phone">Phone</label>
                            <input type="text" class="form-control" id="add-purchaser-phone" name="phone" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="add-purchaser-amount">Amount</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">$</span>
                                </div>
                                <input type="number" step="0.01" class="form-control" id="add-purchaser-amount" name="amount" required>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <div class="custom-control custom-checkbox mt-4">
                                <input type="checkbox" class="custom-control-input" id="add-purchaser-special" name="special_client">
                                <label class="custom-control-label" for="add-purchaser-special">Special Client</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Purchaser</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Purchaser Modal -->
<div class="modal fade" id="deletePurchaserModal" tabindex="-1" role="dialog" aria-labelledby="deletePurchaserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePurchaserModalLabel">Delete Purchaser</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">Are you sure you want to delete this purchaser? This action cannot be undone.</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                <form action="{% url 'purchaser_remove' %}" method="post">
                    {% csrf_token %}
                    <input type="hidden" name="id" id="delete-purchaser-id">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Page level plugins -->
<script src="{% static 'sbadmin/vendor/datatables/jquery.dataTables.min.js' %}"></script>
<script src="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.js' %}"></script>

<!-- Page level custom scripts -->
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#dataTable').DataTable();

        // Edit Purchaser
        $('.edit-purchaser').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var phone = $(this).data('phone');
            var amount = $(this).data('amount');
            var special = $(this).data('special');

            $('#edit-purchaser-id').val(id);
            $('#edit-purchaser-name').val(name);
            $('#edit-purchaser-phone').val(phone);
            $('#edit-purchaser-amount').val(amount);
            $('#edit-purchaser-special').prop('checked', special === true);

            $('#editPurchaserModal').modal('show');
        });

        // Delete Purchaser
        $('.delete-purchaser').click(function() {
            var id = $(this).data('id');
            $('#delete-purchaser-id').val(id);
            $('#deletePurchaserModal').modal('show');
        });

        // Force remove any existing backdrops on page load
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });

    // Custom modal functions
    function openAddModal() {
        // Remove any existing backdrop
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Show modal with custom method
        $('#addPurchaserModal').css({
            'display': 'block',
            'z-index': '9999',
            'position': 'fixed',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(10, 25, 47, 0.8)',
            'overflow': 'auto'
        }).addClass('show');

        // Ensure all elements are interactive
        $('#addPurchaserModal input, #addPurchaserModal textarea, #addPurchaserModal button').css({
            'pointer-events': 'auto',
            'z-index': '10001'
        });

        // Focus first input
        setTimeout(function() {
            $('#add-purchaser-name').focus();
        }, 100);
    }

    function closeAddModal() {
        $('#addPurchaserModal').css('display', 'none').removeClass('show');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    }

    // Fix modal interaction issues
    $('#addPurchaserModal').on('click', function(e) {
        if (e.target === this) {
            closeAddModal();
        }
    });
</script>
{% endblock %}
