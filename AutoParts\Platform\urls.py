from django.urls import path
from . import views

urlpatterns = [
    path('',views.dashboard_view,name='dashboard'),
    path('product/',views.product_view,name='product_view'),
    path('product/add/',views.product_add_view,name='product_add'),
    path('product/remove/',views.product_remove_view,name='product_remove'),
    path('product/edit/',views.product_edit_view,name='product_edit'),
    path('provider/',views.provider_view,name='provider_view'),
    path('provider/add/',views.provider_add_view, name='provider_add'),
    path('provider/edit/',views.provider_edit_view, name='provider_edit'),
    path('provider/remove/',views.provider_remove_view, name='provider_remove'),
    path('purchaser/',views.purchaser_view, name='purchaser_view'),
    path('purchaser/add/',views.purchaser_add_view, name='purchaser_add'),
    path('purchaser/edit/',views.purchaser_edit_view, name='purchaser_edit'),
    path('purchaser/remove/',views.purchaser_remove_view, name='purchaser_remove'),
    path('purchases/',views.purchases_view, name='purchases_view'),
    path('purchases/add/',views.purchases_add_view, name='purchases_add'),
    path('purchases/edit/',views.purchases_edit_view, name='purchases_edit'),
    path('purchases/remove/',views.purchases_remove_view, name='purchases_remove'),
    path('expense/',views.expense_view, name='expense_view'),
    path('expense/add/',views.expense_add_view, name='expense_add'),
    path('expense/edit/',views.expense_edit_view, name='expense_edit'),
    path('expense/remove/',views.expense_remove_view, name='expense_remove'),
    path('login/',views.login_view, name='login_view'),
    path('register/',views.register_view, name='register_view'),
    path('logout/',views.logout_view,name='logout_view'),
    path('set-language/',views.set_language_view, name='set_language'),
    path('profile/',views.profile_view, name='profile_view'),
    path('settings/',views.settings_view, name='settings_view'),
    path('qr-scanner/',views.qr_scanner_view, name='qr_scanner_view')

]