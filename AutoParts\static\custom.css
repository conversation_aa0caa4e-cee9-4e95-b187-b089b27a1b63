/* Custom styles for Auto Parts Management System */
/* Modern Blue Color Scheme */

:root {
    --primary-blue: #0070f3;
    --secondary-blue: #00c6ff;
    --dark-blue: #0a192f;
    --medium-blue: #112240;
    --light-blue: #1e3a8a;
    --accent-blue: #3730a3;
    --gradient-primary: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
    --gradient-dark: linear-gradient(135deg, #0a192f 0%, #112240 100%);
    --gradient-light: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
    --text-light: rgba(255, 255, 255, 0.9);
    --text-muted: rgba(255, 255, 255, 0.7);
    --border-light: rgba(255, 255, 255, 0.1);
    --shadow-blue: rgba(0, 112, 243, 0.3);
}

/* Global body styling */
body {
    background: var(--gradient-dark) !important;
    color: var(--text-light) !important;
    font-family: 'Poppins', sans-serif !important;
}

/* Sidebar styling */
.sidebar {
    background: var(--gradient-dark) !important;
    border-right: 1px solid var(--border-light) !important;
}

.sidebar .nav-item .nav-link {
    color: var(--text-muted) !important;
    transition: all 0.3s ease !important;
}

.sidebar .nav-item .nav-link:hover,
.sidebar .nav-item .nav-link.active {
    color: var(--text-light) !important;
    background: var(--gradient-primary) !important;
    border-radius: 0.5rem !important;
    margin: 0.2rem !important;
    box-shadow: 0 4px 15px var(--shadow-blue) !important;
}

.sidebar .sidebar-brand {
    background: var(--gradient-primary) !important;
    color: white !important;
    border-radius: 0 0 1rem 1rem !important;
}

.sidebar-heading {
    color: var(--secondary-blue) !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}

/* Topbar styling */
.topbar {
    background: var(--gradient-dark) !important;
    border-bottom: 1px solid var(--border-light) !important;
    backdrop-filter: blur(10px) !important;
}

.topbar .navbar-nav .nav-item .nav-link {
    color: var(--text-muted) !important;
}

.topbar .navbar-nav .nav-item .nav-link:hover {
    color: var(--text-light) !important;
}

/* Cards styling */
.card {
    background: rgba(10, 25, 47, 0.8) !important;
    border: 1px solid var(--border-light) !important;
    border-radius: 1rem !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

.card-header {
    background: var(--gradient-primary) !important;
    border-bottom: 1px solid var(--border-light) !important;
    border-radius: 1rem 1rem 0 0 !important;
    color: white !important;
}

.card-body {
    color: var(--text-light) !important;
}

/* Buttons styling */
.btn-primary {
    background: var(--gradient-primary) !important;
    border: none !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 4px 15px var(--shadow-blue) !important;
    transition: all 0.3s ease !important;
}

.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px var(--shadow-blue) !important;
}

.btn-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%) !important;
    border: none !important;
    border-radius: 0.5rem !important;
}

.btn-info {
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%) !important;
    border: none !important;
    border-radius: 0.5rem !important;
}

.btn-warning {
    background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%) !important;
    border: none !important;
    border-radius: 0.5rem !important;
}

.btn-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%) !important;
    border: none !important;
    border-radius: 0.5rem !important;
}

/* Form controls */
.form-control {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid var(--border-light) !important;
    color: var(--text-light) !important;
    border-radius: 0.5rem !important;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: var(--primary-blue) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 112, 243, 0.25) !important;
    color: var(--text-light) !important;
}

.form-control::placeholder {
    color: var(--text-muted) !important;
}

/* Tables */
.table {
    color: var(--text-light) !important;
}

.table thead th {
    background: var(--gradient-primary) !important;
    border: none !important;
    color: white !important;
}

.table tbody tr {
    background: rgba(255, 255, 255, 0.02) !important;
    border-bottom: 1px solid var(--border-light) !important;
}

.table tbody tr:hover {
    background: rgba(0, 112, 243, 0.1) !important;
}

/* Modals */
.modal-content {
    background: var(--gradient-dark) !important;
    border: 1px solid var(--border-light) !important;
    border-radius: 1rem !important;
    color: var(--text-light) !important;
}

.modal-header {
    background: var(--gradient-primary) !important;
    border-bottom: 1px solid var(--border-light) !important;
    border-radius: 1rem 1rem 0 0 !important;
    color: white !important;
}

/* Alerts */
.alert-primary {
    background: rgba(0, 112, 243, 0.1) !important;
    border: 1px solid rgba(0, 112, 243, 0.3) !important;
    color: var(--secondary-blue) !important;
}

.alert-success {
    background: rgba(28, 200, 138, 0.1) !important;
    border: 1px solid rgba(28, 200, 138, 0.3) !important;
    color: #1cc88a !important;
}

.alert-danger {
    background: rgba(231, 74, 59, 0.1) !important;
    border: 1px solid rgba(231, 74, 59, 0.3) !important;
    color: #e74a3b !important;
}

.alert-warning {
    background: rgba(246, 194, 62, 0.1) !important;
    border: 1px solid rgba(246, 194, 62, 0.3) !important;
    color: #f6c23e !important;
}

/* Login and Register page background */
.bg-login-image {
    background: url('/static/service.jpg') !important;
    background-position: center !important;
    background-size: cover !important;
}

.bg-register-image {
    background: url('/static/service.jpg') !important;
    background-position: center !important;
    background-size: cover !important;
}

/* Custom background for the body */
.bg-autoparts {
    background: var(--gradient-dark) !important;
    background-attachment: fixed !important;
}
