/* Custom styles for Auto Parts Management System */
/* Modern Blue Color Scheme - Consistent with Login/Register Pages */

:root {
    --primary-blue: #0070f3;
    --secondary-blue: #00c6ff;
    --dark-blue: #0a192f;
    --medium-blue: #112240;
    --light-blue: #1e3a8a;
    --accent-blue: #3730a3;
    --gradient-primary: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%);
    --gradient-dark: linear-gradient(-45deg, #0a192f, #112240, #1e3a8a, #3730a3);
    --gradient-light: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
    --text-light: rgba(255, 255, 255, 0.9);
    --text-muted: rgba(255, 255, 255, 0.7);
    --border-light: rgba(255, 255, 255, 0.1);
    --shadow-blue: rgba(0, 112, 243, 0.3);
    --glass-bg: rgba(10, 25, 47, 0.8);
    --glass-border: rgba(255, 255, 255, 0.1);
}

/* Global body styling - Animated Background like Login */
body {
    background: var(--dark-blue) !important;
    color: var(--text-light) !important;
    font-family: 'Poppins', sans-serif !important;
    margin: 0 !important;
    overflow-x: hidden !important;
    position: relative !important;
}

/* Animated background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    z-index: -2;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Floating particles background */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0, 198, 255, 0.1), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 112, 243, 0.1), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.05), transparent);
    background-repeat: repeat;
    background-size: 150px 150px;
    animation: float-particles 20s linear infinite;
    z-index: -1;
}

@keyframes float-particles {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-150px); }
}

/* Sidebar styling - Glass Effect */
.sidebar {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) !important;
    border-right: 1px solid var(--glass-border) !important;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3) !important;
}

.sidebar .nav-item .nav-link {
    color: var(--text-muted) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-radius: 0.75rem !important;
    margin: 0.3rem 0.5rem !important;
    padding: 0.75rem 1rem !important;
    position: relative !important;
    overflow: hidden !important;
}

.sidebar .nav-item .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.sidebar .nav-item .nav-link:hover::before {
    left: 100%;
}

.sidebar .nav-item .nav-link:hover,
.sidebar .nav-item .nav-link.active {
    color: white !important;
    background: var(--gradient-primary) !important;
    transform: translateX(8px) scale(1.02) !important;
    box-shadow: 0 8px 25px var(--shadow-blue) !important;
}

.sidebar .sidebar-brand {
    background: var(--gradient-primary) !important;
    color: white !important;
    border-radius: 0 0 1.5rem 1.5rem !important;
    box-shadow: 0 8px 25px var(--shadow-blue) !important;
    position: relative !important;
    overflow: hidden !important;
}

.sidebar .sidebar-brand::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.sidebar-heading {
    color: var(--secondary-blue) !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 2px !important;
    font-size: 0.75rem !important;
    text-shadow: 0 0 10px rgba(0, 198, 255, 0.5) !important;
}

/* Topbar styling - Glass Effect */
.topbar {
    background: var(--glass-bg) !important;
    border-bottom: 1px solid var(--glass-border) !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

.topbar .navbar-nav .nav-item .nav-link {
    color: var(--text-muted) !important;
    transition: all 0.3s ease !important;
    border-radius: 0.5rem !important;
    padding: 0.5rem 1rem !important;
}

.topbar .navbar-nav .nav-item .nav-link:hover {
    color: var(--secondary-blue) !important;
    background: rgba(0, 198, 255, 0.1) !important;
    text-shadow: 0 0 10px rgba(0, 198, 255, 0.5) !important;
}

/* Cards styling - Enhanced Glass Effect */
.card {
    background: var(--glass-bg) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 1.5rem !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
}

.card:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow: 0 25px 50px rgba(0, 112, 243, 0.3) !important;
}

.card-header {
    background: var(--gradient-primary) !important;
    border-bottom: 1px solid var(--glass-border) !important;
    border-radius: 1.5rem 1.5rem 0 0 !important;
    color: white !important;
    font-weight: 600 !important;
    position: relative !important;
    overflow: hidden !important;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: slide 2s infinite;
}

@keyframes slide {
    0% { left: -100%; }
    100% { left: 100%; }
}

.card-body {
    color: var(--text-light) !important;
    position: relative !important;
    z-index: 2 !important;
}

/* Buttons styling - Enhanced with Glass Effect */
.btn {
    border-radius: 0.75rem !important;
    font-weight: 600 !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary) !important;
    border: none !important;
    box-shadow: 0 6px 20px var(--shadow-blue) !important;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 10px 30px rgba(0, 112, 243, 0.5) !important;
}

.btn-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%) !important;
    border: none !important;
    box-shadow: 0 6px 20px rgba(28, 200, 138, 0.3) !important;
}

.btn-success:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 10px 30px rgba(28, 200, 138, 0.5) !important;
}

.btn-info {
    background: linear-gradient(135deg, #36b9cc 0%, #258391 100%) !important;
    border: none !important;
    box-shadow: 0 6px 20px rgba(54, 185, 204, 0.3) !important;
}

.btn-info:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 10px 30px rgba(54, 185, 204, 0.5) !important;
}

.btn-warning {
    background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%) !important;
    border: none !important;
    box-shadow: 0 6px 20px rgba(246, 194, 62, 0.3) !important;
}

.btn-warning:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 10px 30px rgba(246, 194, 62, 0.5) !important;
}

.btn-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%) !important;
    border: none !important;
    box-shadow: 0 6px 20px rgba(231, 74, 59, 0.3) !important;
}

.btn-danger:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 10px 30px rgba(231, 74, 59, 0.5) !important;
}

/* Form controls - Glass Effect */
.form-control {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid var(--glass-border) !important;
    color: var(--text-light) !important;
    border-radius: 0.75rem !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    padding: 0.75rem 1rem !important;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: var(--primary-blue) !important;
    box-shadow: 0 0 0 0.25rem rgba(0, 112, 243, 0.25), 0 0 20px rgba(0, 112, 243, 0.3) !important;
    color: var(--text-light) !important;
    transform: translateY(-2px) !important;
}

.form-control::placeholder {
    color: var(--text-muted) !important;
}

/* Enhanced select styling */
.form-control select {
    background: rgba(255, 255, 255, 0.05) !important;
    color: var(--text-light) !important;
}

.form-control option {
    background: var(--dark-blue) !important;
    color: var(--text-light) !important;
}

/* Tables - Enhanced Glass Effect */
.table {
    color: var(--text-light) !important;
    border-radius: 1rem !important;
    overflow: hidden !important;
    backdrop-filter: blur(10px) !important;
}

.table thead th {
    background: var(--gradient-primary) !important;
    border: none !important;
    color: white !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    padding: 1rem !important;
    position: relative !important;
    overflow: hidden !important;
}

.table thead th::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: slide-header 3s infinite;
}

@keyframes slide-header {
    0% { left: -100%; }
    100% { left: 100%; }
}

.table tbody tr {
    background: rgba(255, 255, 255, 0.02) !important;
    border-bottom: 1px solid var(--glass-border) !important;
    transition: all 0.3s ease !important;
}

.table tbody tr:hover {
    background: rgba(0, 112, 243, 0.15) !important;
    transform: scale(1.01) !important;
    box-shadow: 0 4px 15px rgba(0, 112, 243, 0.2) !important;
}

.table tbody td {
    padding: 1rem !important;
    border: none !important;
    vertical-align: middle !important;
}

/* Table responsive wrapper */
.table-responsive {
    border-radius: 1rem !important;
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid var(--glass-border) !important;
}

/* Modals - Enhanced Glass Effect with Fixed Interactions */
.modal-content {
    background: var(--glass-bg) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 1.5rem !important;
    color: var(--text-light) !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5) !important;
    pointer-events: auto !important;
    z-index: 1050 !important;
    position: relative !important;
}

.modal-header {
    background: var(--gradient-primary) !important;
    border-bottom: 1px solid var(--glass-border) !important;
    border-radius: 1.5rem 1.5rem 0 0 !important;
    color: white !important;
    position: relative !important;
    overflow: hidden !important;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: slide 2s infinite;
}

.modal-body {
    background: transparent !important;
}

.modal-footer {
    background: transparent !important;
    border-top: 1px solid var(--glass-border) !important;
    pointer-events: auto !important;
    z-index: 1050 !important;
}

/* Complete fix for modal interaction issues */
.modal {
    z-index: 9999 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    overflow: auto !important;
    background-color: rgba(10, 25, 47, 0.8) !important;
}

.modal-backdrop {
    display: none !important;
}

.modal-dialog {
    z-index: 10000 !important;
    pointer-events: auto !important;
    margin: 50px auto !important;
    position: relative !important;
}

.modal-body {
    pointer-events: auto !important;
    z-index: 10001 !important;
}

.modal-header {
    pointer-events: auto !important;
    z-index: 10001 !important;
}

/* Ensure form elements are interactive */
.modal .form-control,
.modal .btn,
.modal .form-check-input,
.modal .form-select,
.modal textarea,
.modal input,
.modal select {
    pointer-events: auto !important;
    z-index: 1060 !important;
    position: relative !important;
}

/* Fix any overlay conflicts */
.modal-content * {
    pointer-events: auto !important;
}

.modal-content::before,
.modal-content::after {
    pointer-events: none !important;
}

/* Ensure body doesn't interfere with modals */
body.modal-open {
    overflow: hidden !important;
    padding-right: 0 !important;
}

/* Remove any conflicting overlays */
#page-top::before,
#page-top::after,
.wrapper::before,
.wrapper::after {
    pointer-events: none !important;
    z-index: -1 !important;
}

/* Force modal to be on top of everything */
.modal.show {
    display: block !important;
    z-index: 9999 !important;
}

/* Alerts - Enhanced Glass Effect */
.alert {
    border-radius: 1rem !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid !important;
    position: relative !important;
    overflow: hidden !important;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
}

.alert-primary {
    background: rgba(0, 112, 243, 0.15) !important;
    border-color: rgba(0, 112, 243, 0.3) !important;
    color: var(--secondary-blue) !important;
}

.alert-primary::before {
    background: var(--gradient-primary);
}

.alert-success {
    background: rgba(28, 200, 138, 0.15) !important;
    border-color: rgba(28, 200, 138, 0.3) !important;
    color: #1cc88a !important;
}

.alert-success::before {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
}

.alert-danger {
    background: rgba(231, 74, 59, 0.15) !important;
    border-color: rgba(231, 74, 59, 0.3) !important;
    color: #e74a3b !important;
}

.alert-danger::before {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
}

.alert-warning {
    background: rgba(246, 194, 62, 0.15) !important;
    border-color: rgba(246, 194, 62, 0.3) !important;
    color: #f6c23e !important;
}

.alert-warning::before {
    background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
}

/* Login and Register page background */
.bg-login-image {
    background: url('/static/service.jpg') !important;
    background-position: center !important;
    background-size: cover !important;
}

.bg-register-image {
    background: url('/static/service.jpg') !important;
    background-position: center !important;
    background-size: cover !important;
}

/* Custom background for the body */
.bg-autoparts {
    background: var(--gradient-dark) !important;
    background-attachment: fixed !important;
}

/* Additional enhancements */
/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-blue);
}

/* Text selection */
::selection {
    background: rgba(0, 112, 243, 0.3);
    color: white;
}

/* Focus outline */
*:focus {
    outline: 2px solid rgba(0, 112, 243, 0.5) !important;
    outline-offset: 2px !important;
}

/* Page wrapper enhancements */
#wrapper {
    position: relative;
    z-index: 1;
}

#content-wrapper {
    background: transparent !important;
    position: relative;
    z-index: 2;
}

/* Footer styling */
.sticky-footer {
    background: var(--glass-bg) !important;
    border-top: 1px solid var(--glass-border) !important;
    color: var(--text-muted) !important;
    backdrop-filter: blur(20px) !important;
}

/* Dropdown enhancements */
.dropdown-menu {
    background: var(--glass-bg) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 1rem !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3) !important;
}

.dropdown-item {
    color: var(--text-muted) !important;
    transition: all 0.3s ease !important;
    border-radius: 0.5rem !important;
    margin: 0.2rem !important;
}

.dropdown-item:hover {
    background: var(--gradient-primary) !important;
    color: white !important;
    transform: translateX(5px) !important;
}

/* Badge enhancements */
.badge {
    border-radius: 0.5rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.badge-primary {
    background: var(--gradient-primary) !important;
    box-shadow: 0 2px 10px rgba(0, 112, 243, 0.3) !important;
}

/* Progress bar enhancements */
.progress {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 1rem !important;
    height: 1rem !important;
    overflow: hidden !important;
}

.progress-bar {
    background: var(--gradient-primary) !important;
    border-radius: 1rem !important;
    transition: all 0.3s ease !important;
}

/* Pagination enhancements */
.page-item .page-link {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid var(--glass-border) !important;
    color: var(--text-muted) !important;
    border-radius: 0.5rem !important;
    margin: 0 0.2rem !important;
    transition: all 0.3s ease !important;
}

.page-item .page-link:hover {
    background: var(--gradient-primary) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(0, 112, 243, 0.3) !important;
}

.page-item.active .page-link {
    background: var(--gradient-primary) !important;
    border-color: var(--primary-blue) !important;
    box-shadow: 0 4px 15px rgba(0, 112, 243, 0.3) !important;
}

/* Loading animation */
.loading {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
