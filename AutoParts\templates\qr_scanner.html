{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "QR Code Scanner" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-qrcode fa-fw mr-2"></i>{% custom_trans "QR Code Scanner" %}
        </h1>
        <a href="javascript:history.back()" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> {% custom_trans "Back" %}
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-camera fa-fw mr-2"></i>{% custom_trans "Scan QR Code" %}
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Scanner Container -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="scanner-container">
                                <div id="qr-reader" class="qr-reader"></div>
                                <div id="qr-reader-results" class="mt-3"></div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <!-- Scanner Controls -->
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-cog fa-fw mr-2"></i>{% custom_trans "Scanner Settings" %}
                                    </h5>
                                    
                                    <!-- Camera Selection -->
                                    <div class="form-group">
                                        <label for="camera-select">{% custom_trans "Select Camera" %}:</label>
                                        <select id="camera-select" class="form-control">
                                            <option value="">{% custom_trans "Loading cameras..." %}</option>
                                        </select>
                                    </div>

                                    <!-- Scanner Controls -->
                                    <div class="btn-group-vertical w-100 mb-3">
                                        <button id="start-button" class="btn btn-success mb-2">
                                            <i class="fas fa-play fa-fw mr-2"></i>{% custom_trans "Start Scanner" %}
                                        </button>
                                        <button id="stop-button" class="btn btn-danger mb-2" disabled>
                                            <i class="fas fa-stop fa-fw mr-2"></i>{% custom_trans "Stop Scanner" %}
                                        </button>
                                        <button id="switch-camera" class="btn btn-info">
                                            <i class="fas fa-sync-alt fa-fw mr-2"></i>{% custom_trans "Switch Camera" %}
                                        </button>
                                    </div>

                                    <!-- Scanner Status -->
                                    <div class="alert alert-info" id="scanner-status">
                                        <i class="fas fa-info-circle fa-fw mr-2"></i>
                                        <span id="status-text">{% custom_trans "Ready to scan" %}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Instructions -->
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-info-circle fa-fw mr-2"></i>{% custom_trans "Instructions" %}
                                    </h5>
                                    <ul class="text-sm text-gray-700 mb-0">
                                        <li>{% custom_trans "Allow camera access when prompted" %}</li>
                                        <li>{% custom_trans "Point your camera at the QR code" %}</li>
                                        <li>{% custom_trans "Keep the QR code within the scanning area" %}</li>
                                        <li>{% custom_trans "Ensure good lighting for better results" %}</li>
                                        <li>{% custom_trans "The scanner will automatically detect the code" %}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div class="row mt-4" id="results-section" style="display: none;">
                        <div class="col-12">
                            <div class="card border-left-success shadow">
                                <div class="card-body">
                                    <h5 class="card-title text-success">
                                        <i class="fas fa-check-circle fa-fw mr-2"></i>{% custom_trans "QR Code Detected!" %}
                                    </h5>
                                    <div class="form-group">
                                        <label for="scanned-result">{% custom_trans "Scanned Content" %}:</label>
                                        <textarea id="scanned-result" class="form-control" rows="4" readonly></textarea>
                                    </div>
                                    <div class="btn-group">
                                        <button id="copy-result" class="btn btn-primary">
                                            <i class="fas fa-copy fa-fw mr-2"></i>{% custom_trans "Copy to Clipboard" %}
                                        </button>
                                        <button id="clear-result" class="btn btn-secondary">
                                            <i class="fas fa-trash fa-fw mr-2"></i>{% custom_trans "Clear" %}
                                        </button>
                                        <button id="scan-again" class="btn btn-success">
                                            <i class="fas fa-qrcode fa-fw mr-2"></i>{% custom_trans "Scan Again" %}
                                        </button>
                                        <a href="{% url 'qr_generator_view' %}" class="btn btn-info">
                                            <i class="fas fa-magic fa-fw mr-2"></i>{% custom_trans "Generate QR" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Upload Option -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-left-info shadow">
                                <div class="card-body">
                                    <h5 class="card-title text-info">
                                        <i class="fas fa-upload fa-fw mr-2"></i>{% custom_trans "Upload QR Code Image" %}
                                    </h5>
                                    <p class="text-gray-600">
                                        {% custom_trans "If you have a QR code image file, you can upload it here instead of using the camera." %}
                                    </p>
                                    <div class="custom-file mb-3">
                                        <input type="file" class="custom-file-input" id="qr-file-input" accept="image/*">
                                        <label class="custom-file-label" for="qr-file-input">
                                            {% custom_trans "Choose QR code image..." %}
                                        </label>
                                    </div>
                                    <button id="upload-scan" class="btn btn-info" disabled>
                                        <i class="fas fa-search fa-fw mr-2"></i>{% custom_trans "Scan Uploaded Image" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- QR Scanner Library -->
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

<style>
.qr-reader {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    border: 2px solid #e3e6f0;
    border-radius: 0.5rem;
    overflow: hidden;
}

.scanner-container {
    text-align: center;
}

#qr-reader video {
    width: 100% !important;
    height: auto !important;
    border-radius: 0.5rem;
}

#qr-reader canvas {
    display: none;
}

.card {
    border: none;
    border-radius: 0.5rem;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h6 {
    color: white !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.btn-group-vertical .btn {
    border-radius: 0.35rem !important;
}

#scanner-status {
    font-size: 0.9rem;
}

.custom-file-label::after {
    content: "{% custom_trans 'Browse' %}";
}

@media (max-width: 768px) {
    .qr-reader {
        max-width: 100%;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: 0.35rem !important;
    }
}

/* Animation for successful scan */
@keyframes scanSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.scan-success {
    animation: scanSuccess 0.5s ease-in-out;
}
</style>

<script>
let html5QrcodeScanner = null;
let isScanning = false;
let cameras = [];
let currentCameraIndex = 0;

document.addEventListener('DOMContentLoaded', function() {
    initializeScanner();
    setupEventListeners();
});

function initializeScanner() {
    // Get available cameras
    Html5Qrcode.getCameras().then(devices => {
        cameras = devices;
        populateCameraSelect(devices);
        updateStatus('{% custom_trans "Cameras loaded. Ready to scan." %}', 'info');
    }).catch(err => {
        console.error('Error getting cameras:', err);
        updateStatus('{% custom_trans "Error accessing cameras. Please check permissions." %}', 'danger');
    });
}

function populateCameraSelect(devices) {
    const select = document.getElementById('camera-select');
    select.innerHTML = '';
    
    if (devices.length === 0) {
        select.innerHTML = '<option value="">{% custom_trans "No cameras found" %}</option>';
        return;
    }
    
    devices.forEach((device, index) => {
        const option = document.createElement('option');
        option.value = device.id;
        option.textContent = device.label || `{% custom_trans "Camera" %} ${index + 1}`;
        select.appendChild(option);
    });
}

function setupEventListeners() {
    // Start scanner
    document.getElementById('start-button').addEventListener('click', startScanner);
    
    // Stop scanner
    document.getElementById('stop-button').addEventListener('click', stopScanner);
    
    // Switch camera
    document.getElementById('switch-camera').addEventListener('click', switchCamera);
    
    // Camera selection change
    document.getElementById('camera-select').addEventListener('change', function() {
        if (isScanning) {
            stopScanner();
            setTimeout(startScanner, 500);
        }
    });
    
    // File upload
    document.getElementById('qr-file-input').addEventListener('change', handleFileSelect);
    document.getElementById('upload-scan').addEventListener('click', scanUploadedFile);
    
    // Result actions
    document.getElementById('copy-result').addEventListener('click', copyResult);
    document.getElementById('clear-result').addEventListener('click', clearResult);
    document.getElementById('scan-again').addEventListener('click', scanAgain);
}

function startScanner() {
    const selectedCameraId = document.getElementById('camera-select').value;
    
    if (!selectedCameraId) {
        updateStatus('{% custom_trans "Please select a camera first." %}', 'warning');
        return;
    }
    
    const config = {
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0
    };
    
    html5QrcodeScanner = new Html5Qrcode("qr-reader");
    
    html5QrcodeScanner.start(
        selectedCameraId,
        config,
        onScanSuccess,
        onScanFailure
    ).then(() => {
        isScanning = true;
        document.getElementById('start-button').disabled = true;
        document.getElementById('stop-button').disabled = false;
        updateStatus('{% custom_trans "Scanner active. Point camera at QR code." %}', 'success');
    }).catch(err => {
        console.error('Error starting scanner:', err);
        updateStatus('{% custom_trans "Error starting scanner. Please try again." %}', 'danger');
    });
}

function stopScanner() {
    if (html5QrcodeScanner && isScanning) {
        html5QrcodeScanner.stop().then(() => {
            isScanning = false;
            document.getElementById('start-button').disabled = false;
            document.getElementById('stop-button').disabled = true;
            updateStatus('{% custom_trans "Scanner stopped." %}', 'info');
        }).catch(err => {
            console.error('Error stopping scanner:', err);
        });
    }
}

function switchCamera() {
    if (cameras.length <= 1) {
        updateStatus('{% custom_trans "Only one camera available." %}', 'warning');
        return;
    }
    
    currentCameraIndex = (currentCameraIndex + 1) % cameras.length;
    const select = document.getElementById('camera-select');
    select.selectedIndex = currentCameraIndex;
    
    if (isScanning) {
        stopScanner();
        setTimeout(startScanner, 500);
    }
}

function onScanSuccess(decodedText, decodedResult) {
    // Stop scanner after successful scan
    stopScanner();
    
    // Show results
    showScanResult(decodedText);
    
    // Add success animation
    document.getElementById('results-section').classList.add('scan-success');
    setTimeout(() => {
        document.getElementById('results-section').classList.remove('scan-success');
    }, 500);
    
    updateStatus('{% custom_trans "QR code scanned successfully!" %}', 'success');
}

function onScanFailure(error) {
    // This is called continuously while scanning, so we don't show errors
    // unless it's a critical error
}

function showScanResult(result) {
    document.getElementById('scanned-result').value = result;
    document.getElementById('results-section').style.display = 'block';
    
    // Scroll to results
    document.getElementById('results-section').scrollIntoView({ 
        behavior: 'smooth',
        block: 'center'
    });
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    const uploadButton = document.getElementById('upload-scan');
    const label = document.querySelector('.custom-file-label');
    
    if (file) {
        label.textContent = file.name;
        uploadButton.disabled = false;
    } else {
        label.textContent = '{% custom_trans "Choose QR code image..." %}';
        uploadButton.disabled = true;
    }
}

function scanUploadedFile() {
    const fileInput = document.getElementById('qr-file-input');
    const file = fileInput.files[0];
    
    if (!file) {
        updateStatus('{% custom_trans "Please select a file first." %}', 'warning');
        return;
    }
    
    updateStatus('{% custom_trans "Scanning uploaded image..." %}', 'info');
    
    Html5Qrcode.scanFile(file, true)
        .then(decodedText => {
            showScanResult(decodedText);
            updateStatus('{% custom_trans "Image scanned successfully!" %}', 'success');
        })
        .catch(err => {
            console.error('Error scanning file:', err);
            updateStatus('{% custom_trans "No QR code found in the uploaded image." %}', 'danger');
        });
}

function copyResult() {
    const resultText = document.getElementById('scanned-result').value;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(resultText).then(() => {
            updateStatus('{% custom_trans "Result copied to clipboard!" %}', 'success');
        }).catch(err => {
            console.error('Error copying to clipboard:', err);
            fallbackCopyTextToClipboard(resultText);
        });
    } else {
        fallbackCopyTextToClipboard(resultText);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            updateStatus('{% custom_trans "Result copied to clipboard!" %}', 'success');
        } else {
            updateStatus('{% custom_trans "Failed to copy result." %}', 'danger');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        updateStatus('{% custom_trans "Failed to copy result." %}', 'danger');
    }
    
    document.body.removeChild(textArea);
}

function clearResult() {
    document.getElementById('scanned-result').value = '';
    document.getElementById('results-section').style.display = 'none';
    updateStatus('{% custom_trans "Result cleared." %}', 'info');
}

function scanAgain() {
    clearResult();
    startScanner();
}

function updateStatus(message, type) {
    const statusElement = document.getElementById('scanner-status');
    const statusText = document.getElementById('status-text');
    
    // Remove existing alert classes
    statusElement.className = 'alert';
    
    // Add new alert class
    statusElement.classList.add(`alert-${type}`);
    
    // Update text
    statusText.textContent = message;
    
    // Auto-hide success/info messages after 3 seconds
    if (type === 'success' || type === 'info') {
        setTimeout(() => {
            if (statusText.textContent === message) {
                updateStatus('{% custom_trans "Ready to scan" %}', 'info');
            }
        }, 3000);
    }
}

// Cleanup when page is unloaded
window.addEventListener('beforeunload', function() {
    if (html5QrcodeScanner && isScanning) {
        html5QrcodeScanner.stop();
    }
});
</script>
{% endblock %}
