{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Add Purchase" %} - Auto Parts Management{% endblock %}

{% block extra_css %}
<style>
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1.5rem !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
    }

    .form-card {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-radius: 0.5rem !important;
    }

    .btn-submit {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.75rem !important;
        padding: 0.75rem 2rem !important;
        font-weight: 600 !important;
        color: white !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.3) !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <h1 class="h3 mb-0" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important;">
        <i class="fas fa-plus-circle fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important;"></i>
        {% custom_trans "Add Purchase" %}
    </h1>
    <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Add a new purchase to your system" %}</p>
</div>

<!-- Add Purchase Form -->
<div class="card form-card shadow mb-4">
    <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
        <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
            <i class="fas fa-shopping-cart fa-fw mr-2"></i>{% custom_trans "Purchase Information" %}
        </h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchases_add' %}" method="post">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="purchaser">Purchaser</label>
                    <select class="form-control" id="purchaser" name="purchaser" required>
                        <option value="">Select Purchaser</option>
                        {% for purchaser in purchasers %}
                        <option value="{{ purchaser.id }}">{{ purchaser.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group col-md-6">
                    <label for="product">Product</label>
                    <select class="form-control" id="product" name="product" required>
                        <option value="">Select Product</option>
                        {% for product in products %}
                        <option value="{{ product.id }}" data-price="{{ product.price }}" data-bulk-price="{{ product.bulk_price }}">{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-4">
                    <label for="quantity">Quantity</label>
                    <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                </div>
                <div class="form-group col-md-4">
                    <label for="price">Unit Price</label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text" class="form-control" id="price" readonly>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    <label for="total">Total</label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input type="text" class="form-control" id="total" readonly>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-6">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="debt" name="debt">
                        <label class="custom-control-label" for="debt">Debt</label>
                    </div>
                </div>
                <div class="form-group col-md-6">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="bulk" name="bulk">
                        <label class="custom-control-label" for="bulk">Bulk</label>
                    </div>
                </div>
            </div>
            <div class="text-center">
                <button type="submit" class="btn btn-submit mr-3">
                    <i class="fas fa-plus-circle fa-fw mr-1"></i>{% custom_trans "Add Purchase" %}
                </button>
                <a href="{% url 'purchases_view' %}" class="btn btn-secondary" style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white; padding: 0.75rem 2rem;">
                    <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Cancel" %}
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Update price and total when product or bulk changes
        function updatePriceAndTotal() {
            var selectedOption = $('#product option:selected');
            var price = 0;
            
            if (selectedOption.val()) {
                if ($('#bulk').is(':checked')) {
                    price = parseFloat(selectedOption.data('bulk-price'));
                } else {
                    price = parseFloat(selectedOption.data('price'));
                }
            }
            
            $('#price').val(price.toFixed(2));
            
            var quantity = parseInt($('#quantity').val()) || 0;
            var total = price * quantity;
            $('#total').val(total.toFixed(2));
        }
        
        $('#product').change(updatePriceAndTotal);
        $('#bulk').change(updatePriceAndTotal);
        $('#quantity').on('input', updatePriceAndTotal);
    });
</script>
{% endblock %}
