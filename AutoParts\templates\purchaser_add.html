{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Add Purchaser" %} - Auto Parts Management{% endblock %}

{% block extra_css %}
<style>
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1.5rem !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
    }

    .form-card {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-radius: 0.5rem !important;
    }

    .btn-submit {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.75rem !important;
        padding: 0.75rem 2rem !important;
        font-weight: 600 !important;
        color: white !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.3) !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <h1 class="h3 mb-0" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important;">
        <i class="fas fa-plus-circle fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important;"></i>
        {% custom_trans "Add Purchaser" %}
    </h1>
    <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7);">{% custom_trans "Add a new purchaser to your system" %}</p>
</div>

<!-- Add Purchaser Form -->
<div class="card form-card shadow mb-4">
    <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
        <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
            <i class="fas fa-users fa-fw mr-2"></i>{% custom_trans "Purchaser Information" %}
        </h6>
    </div>
    <div class="card-body">
        <form action="{% url 'purchaser_add' %}" method="post">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="name" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-user fa-fw mr-1"></i>{% custom_trans "Name" %}
                    </label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="form-group col-md-6">
                    <label for="phone" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-phone fa-fw mr-1"></i>{% custom_trans "Phone" %}
                    </label>
                    <input type="text" class="form-control" id="phone" name="phone" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="amount" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-dollar-sign fa-fw mr-1"></i>{% custom_trans "Amount" %}
                    </label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text" style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.7);">$</span>
                        </div>
                        <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
                    </div>
                </div>
                <div class="form-group col-md-6">
                    <div class="custom-control custom-checkbox mt-4">
                        <input type="checkbox" class="custom-control-input" id="special_client" name="special_client">
                        <label class="custom-control-label" for="special_client" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                            <i class="fas fa-star fa-fw mr-1"></i>{% custom_trans "Special Client" %}
                        </label>
                    </div>
                </div>
            </div>
            <div class="text-center">
                <button type="submit" class="btn btn-submit mr-3">
                    <i class="fas fa-plus-circle fa-fw mr-1"></i>{% custom_trans "Add Purchaser" %}
                </button>
                <a href="{% url 'purchaser_view' %}" class="btn btn-secondary" style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white; padding: 0.75rem 2rem;">
                    <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Cancel" %}
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
