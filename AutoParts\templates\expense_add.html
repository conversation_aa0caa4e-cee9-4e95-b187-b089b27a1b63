{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Add Expense" %} - Auto Parts Management{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced page styling matching other pages */
    .page-heading {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1.5rem !important;
        padding: 2rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .form-card {
        background: rgba(255, 255, 255, 0.02) !important;
        border-radius: 1.5rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        overflow: hidden !important;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-radius: 0.5rem !important;
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: #0070f3 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 112, 243, 0.25) !important;
        color: rgba(255, 255, 255, 0.9) !important;
    }

    .btn-submit {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.75rem !important;
        padding: 0.75rem 2rem !important;
        font-weight: 600 !important;
        color: white !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.3) !important;
    }

    .btn-submit:hover {
        transform: translateY(-3px) scale(1.05) !important;
        box-shadow: 0 10px 30px rgba(0, 112, 243, 0.5) !important;
        color: white !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="page-heading">
    <div class="d-sm-flex align-items-center justify-content-between">
        <div>
            <h1 class="h3 mb-0" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important;">
                <i class="fas fa-plus-circle fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important; -webkit-background-clip: text !important; -webkit-text-fill-color: transparent !important; background-clip: text !important; animation: pulse-icon 2s infinite ease-in-out;"></i>
                {% custom_trans "Add Expense" %}
            </h1>
            <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7); font-size: 1rem;">
                {% custom_trans "Add a new expense to your system" %}
            </p>
        </div>
    </div>
</div>

<!-- Add Expense Form -->
<div class="card form-card shadow mb-4">
    <div class="card-header py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(0, 112, 243, 0.2); box-shadow: 0 2px 10px rgba(0, 112, 243, 0.1);">
        <h6 class="m-0 font-weight-bold" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
            <i class="fas fa-receipt fa-fw mr-2" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;"></i>
            {% custom_trans "Expense Information" %}
        </h6>
    </div>
    <div class="card-body">
        <form action="{% url 'expense_add' %}" method="post">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="provider" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-truck fa-fw mr-1"></i>{% custom_trans "Provider" %}
                    </label>
                    <select class="form-control" id="provider" name="provider" required>
                        <option value="">{% custom_trans "Select Provider" %}</option>
                        {% for provider in providers %}
                        <option value="{{ provider.id }}">{{ provider.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group col-md-6">
                    <label for="product" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-box fa-fw mr-1"></i>{% custom_trans "Product" %}
                    </label>
                    <select class="form-control" id="product" name="product" required>
                        <option value="">{% custom_trans "Select Product" %}</option>
                        {% for product in products %}
                        <option value="{{ product.id }}">{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-4">
                    <label for="quantity" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-sort-numeric-up fa-fw mr-1"></i>{% custom_trans "Quantity" %}
                    </label>
                    <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                </div>
                <div class="form-group col-md-4">
                    <label for="price" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-dollar-sign fa-fw mr-1"></i>{% custom_trans "Price per Unit" %}
                    </label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text" style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.7);">$</span>
                        </div>
                        <input type="number" step="0.01" class="form-control" id="price" name="price" required>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    <label for="total" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                        <i class="fas fa-calculator fa-fw mr-1"></i>{% custom_trans "Total" %}
                    </label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text" style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.7);">$</span>
                        </div>
                        <input type="text" class="form-control" id="total" readonly>
                    </div>
                </div>
            </div>
            <div class="text-center">
                <button type="submit" class="btn btn-submit mr-3">
                    <i class="fas fa-plus-circle fa-fw mr-1"></i>{% custom_trans "Add Expense" %}
                </button>
                <a href="{% url 'expense_view' %}" class="btn btn-secondary"
                   style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white; padding: 0.75rem 2rem;">
                    <i class="fas fa-times fa-fw mr-1"></i>{% custom_trans "Cancel" %}
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Calculate total when quantity or price changes
        function calculateTotal() {
            var quantity = parseInt($('#quantity').val()) || 0;
            var price = parseFloat($('#price').val()) || 0;
            var total = quantity * price;
            $('#total').val(total.toFixed(2));
        }
        
        $('#quantity, #price').on('input', calculateTotal);
    });
</script>
{% endblock %}
