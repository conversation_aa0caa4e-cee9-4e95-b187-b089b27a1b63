{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Backup Tokens" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-key fa-fw mr-2"></i>{% custom_trans "Backup Tokens" %}
        </h1>
        <a href="{% url 'profile_view' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> {% custom_trans "Back to Profile" %}
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-8 col-lg-10">
            <!-- Current Tokens Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Your Backup Tokens" %}
                    </h6>
                </div>
                <div class="card-body">
                    {% if backup_tokens %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle fa-fw mr-2"></i>
                            <strong>{% custom_trans "Important:" %}</strong> 
                            {% custom_trans "Each backup token can only be used once. Keep them in a safe place!" %}
                        </div>

                        <div class="row">
                            {% for token in backup_tokens %}
                                <div class="col-md-6 mb-3">
                                    <div class="card {% if token.used %}bg-light{% else %}bg-success text-white{% endif %}">
                                        <div class="card-body text-center py-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <code class="h6 mb-0 {% if not token.used %}text-white{% endif %}">
                                                    {{ token.token }}
                                                </code>
                                                {% if token.used %}
                                                    <span class="badge badge-secondary">
                                                        <i class="fas fa-check fa-fw"></i> {% custom_trans "Used" %}
                                                    </span>
                                                {% else %}
                                                    <span class="badge badge-light text-success">
                                                        <i class="fas fa-shield-alt fa-fw"></i> {% custom_trans "Available" %}
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Statistics -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card border-left-success shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                    {% custom_trans "Available Tokens" %}
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                    {{ available_tokens_count }}
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-key fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-left-warning shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                    {% custom_trans "Used Tokens" %}
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                    {{ used_tokens_count }}
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-key fa-5x text-gray-300 mb-3"></i>
                            <h4 class="text-gray-600">{% custom_trans "No Backup Tokens" %}</h4>
                            <p class="text-gray-500">
                                {% custom_trans "You don't have any backup tokens yet. Generate some to secure your account." %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cogs fa-fw mr-2"></i>{% custom_trans "Manage Tokens" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-primary shadow h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">
                                        <i class="fas fa-plus-circle fa-fw mr-2"></i>{% custom_trans "Generate New Tokens" %}
                                    </h5>
                                    <p class="card-text text-gray-600">
                                        {% custom_trans "Create new backup tokens. This will replace your existing tokens." %}
                                    </p>
                                    <form method="post" action="{% url 'two_factor:backup_tokens' %}">
                                        {% csrf_token %}
                                        <button type="submit" name="generate" class="btn btn-primary">
                                            <i class="fas fa-sync-alt fa-fw mr-2"></i>{% custom_trans "Generate Tokens" %}
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-info shadow h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-info">
                                        <i class="fas fa-download fa-fw mr-2"></i>{% custom_trans "Download Tokens" %}
                                    </h5>
                                    <p class="card-text text-gray-600">
                                        {% custom_trans "Download your backup tokens as a text file for safekeeping." %}
                                    </p>
                                    {% if backup_tokens %}
                                        <button type="button" class="btn btn-info" onclick="downloadTokens()">
                                            <i class="fas fa-file-download fa-fw mr-2"></i>{% custom_trans "Download" %}
                                        </button>
                                    {% else %}
                                        <button type="button" class="btn btn-info" disabled>
                                            <i class="fas fa-file-download fa-fw mr-2"></i>{% custom_trans "No Tokens Available" %}
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if backup_tokens %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card border-left-warning shadow">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning">
                                            <i class="fas fa-exclamation-triangle fa-fw mr-2"></i>{% custom_trans "Disable Two-Factor Authentication" %}
                                        </h5>
                                        <p class="card-text text-gray-600">
                                            {% custom_trans "Remove two-factor authentication from your account. This will make your account less secure." %}
                                        </p>
                                        <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#disableModal">
                                            <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Disable 2FA" %}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Disable 2FA Modal -->
<div class="modal fade" id="disableModal" tabindex="-1" role="dialog" aria-labelledby="disableModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="disableModalLabel">
                    <i class="fas fa-exclamation-triangle fa-fw mr-2 text-warning"></i>
                    {% custom_trans "Disable Two-Factor Authentication" %}
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle fa-fw mr-2"></i>
                    <strong>{% custom_trans "Warning:" %}</strong> 
                    {% custom_trans "Disabling two-factor authentication will make your account less secure." %}
                </div>
                <p>{% custom_trans "Are you sure you want to disable two-factor authentication for your account?" %}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times fa-fw mr-2"></i>{% custom_trans "Cancel" %}
                </button>
                <form method="post" action="{% url 'two_factor:disable' %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Yes, Disable 2FA" %}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 0.5rem;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h6 {
    color: white !important;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
</style>

<script>
function downloadTokens() {
    const tokens = [
        {% for token in backup_tokens %}
            "{{ token.token }}"{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    const content = "Auto Parts Management System - Backup Tokens\n" +
                   "Generated on: " + new Date().toLocaleString() + "\n" +
                   "Username: {{ request.user.username }}\n\n" +
                   "IMPORTANT: Keep these tokens safe and secure!\n" +
                   "Each token can only be used once.\n\n" +
                   "Backup Tokens:\n" +
                   tokens.map((token, index) => `${index + 1}. ${token}`).join('\n');
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'autoparts-backup-tokens.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>
{% endblock %}
