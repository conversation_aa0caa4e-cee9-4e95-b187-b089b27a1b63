{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Settings" %}{% endblock %}

{% block extra_css %}
<style>
    /* Settings page enhancements */
    .settings-header {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1rem !important;
        padding: 1.5rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .settings-card {
        background: rgba(10, 25, 47, 0.8) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 1rem !important;
        backdrop-filter: blur(10px) !important;
        transition: all 0.3s ease !important;
    }

    .settings-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 15px 35px rgba(0, 112, 243, 0.2) !important;
    }

    .settings-icon {
        font-size: 3rem !important;
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        animation: pulse-icon 2s infinite ease-in-out !important;
    }

    @keyframes pulse-icon {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .language-option-card {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 0.5rem !important;
        padding: 1rem !important;
        margin-bottom: 1rem !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
    }

    .language-option-card:hover {
        background: rgba(0, 112, 243, 0.1) !important;
        border-color: #0070f3 !important;
        transform: translateX(5px) !important;
    }

    .language-option-card.active {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.2) 0%, rgba(0, 198, 255, 0.2) 100%) !important;
        border-color: #0070f3 !important;
    }

    .language-flag {
        font-size: 2rem !important;
        margin-right: 1rem !important;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
    }

    .btn-save-settings {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.5rem !important;
        padding: 0.75rem 2rem !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 15px rgba(0, 112, 243, 0.3) !important;
    }

    .btn-save-settings:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.4) !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="settings-header">
    <div class="text-center">
        <i class="fas fa-cogs settings-icon mb-3"></i>
        <h1 class="h3 mb-0">
            {% custom_trans "Settings" %}
        </h1>
        <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7);">
            {% custom_trans "Customize your application preferences" %}
        </p>
    </div>
</div>

<!-- Settings Content -->
<div class="row">
    <!-- Language Settings Card -->
    <div class="col-lg-6">
        <div class="card settings-card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold" style="color: white;">
                    <i class="fas fa-globe fa-fw mr-2"></i>{% custom_trans "Language Settings" %}
                </h6>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="language">{% custom_trans "Select Language" %}:</label>
                        <select class="form-control" id="language" name="language">
                            <option value="en" {% if current_language == 'en' %}selected{% endif %}>
                                🇺🇸 {% custom_trans "English" %}
                            </option>
                            <option value="ar" {% if current_language == 'ar' %}selected{% endif %}>
                                🇸🇦 {% custom_trans "Arabic" %}
                            </option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save fa-fw mr-2"></i>{% custom_trans "Save Language" %}
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Account Settings Card -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user-cog fa-fw mr-2"></i>{% custom_trans "Account Settings" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{% url 'profile_view' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user fa-fw mr-2 text-primary"></i>
                        {% custom_trans "Edit Profile" %}
                        <i class="fas fa-chevron-right float-right mt-1"></i>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-toggle="modal" data-target="#changePasswordModal">
                        <i class="fas fa-key fa-fw mr-2 text-warning"></i>
                        {% custom_trans "Change Password" %}
                        <i class="fas fa-chevron-right float-right mt-1"></i>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-toggle="modal" data-target="#logoutModal">
                        <i class="fas fa-sign-out-alt fa-fw mr-2 text-danger"></i>
                        {% custom_trans "Logout" %}
                        <i class="fas fa-chevron-right float-right mt-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information Card -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle fa-fw mr-2"></i>{% custom_trans "System Information" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% custom_trans "Application Name" %}:</strong></td>
                                <td>{% custom_trans "Auto Parts Management System" %}</td>
                            </tr>
                            <tr>
                                <td><strong>{% custom_trans "Version" %}:</strong></td>
                                <td>1.0.0</td>
                            </tr>
                            <tr>
                                <td><strong>{% custom_trans "Current User" %}:</strong></td>
                                <td>{{ request.user.username }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% custom_trans "Current Language" %}:</strong></td>
                                <td>
                                    {% if current_language == 'ar' %}
                                        🇸🇦 {% custom_trans "Arabic" %}
                                    {% else %}
                                        🇺🇸 {% custom_trans "English" %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{% custom_trans "Session Status" %}:</strong></td>
                                <td><span class="badge badge-success">{% custom_trans "Active" %}</span></td>
                            </tr>
                            <tr>
                                <td><strong>{% custom_trans "Last Login" %}:</strong></td>
                                <td>{{ request.user.last_login|date:"F d, Y H:i"|default:"Never" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" role="dialog" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">
                    <i class="fas fa-key fa-fw mr-2"></i>{% custom_trans "Change Password" %}
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="form-group">
                        <label for="current_password">{% custom_trans "Current Password" %}</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="form-group">
                        <label for="new_password">{% custom_trans "New Password" %}</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">{% custom_trans "Confirm New Password" %}</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times fa-fw mr-2"></i>{% custom_trans "Cancel" %}
                    </button>
                    <button type="submit" name="change_password" class="btn btn-warning">
                        <i class="fas fa-key fa-fw mr-2"></i>{% custom_trans "Change Password" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
