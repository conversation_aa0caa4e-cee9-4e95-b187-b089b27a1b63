{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Two-Factor Authentication Enabled" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-check-circle fa-fw mr-2 text-success"></i>{% custom_trans "Setup Complete" %}
        </h1>
        <a href="{% url 'dashboard' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-home fa-sm text-white-50"></i> {% custom_trans "Go to Dashboard" %}
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-8 col-lg-10">
            <!-- Success Message -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-success">
                    <h6 class="m-0 font-weight-bold text-white">
                        <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Two-Factor Authentication Enabled" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="mb-3">
                            <i class="fas fa-check-circle fa-5x text-success"></i>
                        </div>
                        <h4 class="text-gray-900">{% custom_trans "Congratulations!" %}</h4>
                        <p class="text-gray-600 lead">
                            {% custom_trans "Two-factor authentication has been successfully enabled for your account." %}
                        </p>
                    </div>

                    <div class="alert alert-success">
                        <i class="fas fa-info-circle fa-fw mr-2"></i>
                        <strong>{% custom_trans "Your account is now more secure!" %}</strong><br>
                        {% custom_trans "From now on, you'll need both your password and a verification code from your authenticator app to log in." %}
                    </div>

                    <!-- Security Benefits -->
                    <div class="row mb-4">
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body text-center">
                                    <div class="mb-2">
                                        <i class="fas fa-lock fa-3x text-success"></i>
                                    </div>
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        {% custom_trans "Enhanced Security" %}
                                    </div>
                                    <div class="text-xs text-gray-800">
                                        {% custom_trans "Extra layer of protection" %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body text-center">
                                    <div class="mb-2">
                                        <i class="fas fa-mobile-alt fa-3x text-info"></i>
                                    </div>
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        {% custom_trans "Mobile Protection" %}
                                    </div>
                                    <div class="text-xs text-gray-800">
                                        {% custom_trans "Codes generated on your phone" %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body text-center">
                                    <div class="mb-2">
                                        <i class="fas fa-key fa-3x text-warning"></i>
                                    </div>
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        {% custom_trans "Backup Tokens" %}
                                    </div>
                                    <div class="text-xs text-gray-800">
                                        {% custom_trans "Emergency access codes" %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list-check fa-fw mr-2"></i>{% custom_trans "What's Next?" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-primary shadow h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">
                                        <i class="fas fa-key fa-fw mr-2"></i>{% custom_trans "Save Your Backup Tokens" %}
                                    </h5>
                                    <p class="card-text text-gray-600">
                                        {% custom_trans "Download and securely store your backup tokens for emergency access." %}
                                    </p>
                                    <a href="{% url 'two_factor:backup_tokens' %}" class="btn btn-primary">
                                        <i class="fas fa-download fa-fw mr-2"></i>{% custom_trans "Manage Tokens" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-success shadow h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-success">
                                        <i class="fas fa-mobile-alt fa-fw mr-2"></i>{% custom_trans "Test Your Setup" %}
                                    </h5>
                                    <p class="card-text text-gray-600">
                                        {% custom_trans "Try logging out and back in to make sure everything works correctly." %}
                                    </p>
                                    <a href="{% url 'logout_view' %}" class="btn btn-success">
                                        <i class="fas fa-sign-out-alt fa-fw mr-2"></i>{% custom_trans "Test Login" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Important Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle fa-fw mr-2"></i>{% custom_trans "Important Information" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">
                            <i class="fas fa-mobile-alt fa-fw mr-2"></i>{% custom_trans "Don't Lose Your Phone!" %}
                        </h5>
                        <p class="mb-0">
                            {% custom_trans "Make sure to keep your authenticator app safe and consider backing up your phone. If you lose access to your authenticator app, you can use your backup tokens to regain access." %}
                        </p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-gray-900">
                                <i class="fas fa-info-circle fa-fw mr-2"></i>{% custom_trans "Remember:" %}
                            </h6>
                            <ul class="text-gray-700">
                                <li>{% custom_trans "Keep your backup tokens in a safe place" %}</li>
                                <li>{% custom_trans "Each backup token can only be used once" %}</li>
                                <li>{% custom_trans "You can generate new backup tokens anytime" %}</li>
                                <li>{% custom_trans "You can disable 2FA from your profile settings" %}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-gray-900">
                                <i class="fas fa-mobile-alt fa-fw mr-2"></i>{% custom_trans "Supported Apps:" %}
                            </h6>
                            <ul class="text-gray-700">
                                <li>Google Authenticator</li>
                                <li>Microsoft Authenticator</li>
                                <li>Authy</li>
                                <li>{% custom_trans "Any TOTP-compatible app" %}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="text-center mb-4">
                <a href="{% url 'dashboard' %}" class="btn btn-primary btn-lg mr-3">
                    <i class="fas fa-home fa-fw mr-2"></i>{% custom_trans "Go to Dashboard" %}
                </a>
                <a href="{% url 'profile_view' %}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-user fa-fw mr-2"></i>{% custom_trans "View Profile" %}
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 0.5rem;
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header.bg-success {
    background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%) !important;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    border-radius: 0.5rem;
}

.alert-heading {
    margin-bottom: 0.5rem;
}

.fa-5x {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
</style>

<script>
// Auto-redirect to dashboard after 30 seconds
setTimeout(function() {
    if (confirm('{% custom_trans "Would you like to go to the dashboard now?" %}')) {
        window.location.href = '{% url "dashboard" %}';
    }
}, 30000);

// Show success animation
document.addEventListener('DOMContentLoaded', function() {
    // Add a subtle animation to the success icon
    const successIcon = document.querySelector('.fa-check-circle');
    if (successIcon) {
        successIcon.style.opacity = '0';
        successIcon.style.transform = 'scale(0.5)';
        
        setTimeout(() => {
            successIcon.style.transition = 'all 0.5s ease-out';
            successIcon.style.opacity = '1';
            successIcon.style.transform = 'scale(1)';
        }, 500);
    }
});
</script>
{% endblock %}
