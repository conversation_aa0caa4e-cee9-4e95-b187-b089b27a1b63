from django.db import models
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.contrib.auth.base_user import BaseUserManager
# Create your models here.
class Product(models.Model):
    name = models.CharField(max_length=50)
    reference = models.CharField(max_length=50)
    brand = models.CharField(max_length=20)
    model = models.CharField(max_length=30)
    description = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    bulk_price = models.DecimalField(max_digits=10, decimal_places=2)
    def __str__(self):
        return self.name


class Provider(models.Model):
    name= models.CharField(max_length=30)
    phone = models.CharField(max_length=10)
    def __str__(self):
        return self.name


class Purchaser(models.Model):
    name= models.CharField(max_length=30)
    phone = models.CharField(max_length=10)
    amount = models.DecimalField(max_digits=20, decimal_places=2)
    special_client = models.BooleanField(default=False)
    def __str__(self):
        return self.name

class Purchases(models.Model):
    purchaser = models.ForeignKey(Purchaser,on_delete=models.CASCADE)
    product = models.ForeignKey(Product,on_delete=models.CASCADE)
    quantity = models.IntegerField()
    debt = models.BooleanField(default=False)
    bulk = models.BooleanField(default=False)
    date = models.DateTimeField(auto_now_add=True)
    def __str__(self):
        return str(self.purchaser) + ' purchase a ' + str(self.product)

class Expense(models.Model):
    provider = models.ForeignKey(Provider,on_delete=models.CASCADE)
    product = models.ForeignKey(Product,on_delete=models.CASCADE)
    quantity = models.IntegerField()
    price = models.DecimalField(null=True,max_digits=10, decimal_places=2)
    date = models.DateTimeField(auto_now_add=True)
    def __str__(self):
        return str(self.provider) + ' expense a ' + str(self.product)

class Stock(models.Model):
    product = models.ForeignKey(Product,on_delete=models.CASCADE)
    quantity_total = models.IntegerField()
    def __str__(self):
        return str(self.product) + ' stock'

class EmployerManager(BaseUserManager):
    def create_user(self, username, password=None, **extra_fields):
        if not username:
            raise ValueError('The Username field must be set')
        user = self.model(username=username, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(username, password, **extra_fields)

class Employer(AbstractBaseUser, PermissionsMixin):
    username = models.CharField(max_length=50, unique=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    date_joined = models.DateTimeField(auto_now_add=True)

    objects = EmployerManager()

    REQUIRED_FIELDS = []
    USERNAME_FIELD = 'username'

    def __str__(self):
        return self.username
