{% extends 'base.html' %}
{% load static %}

{% block title %}Products - Auto Parts Management{% endblock %}

{% block extra_css %}
<!-- Custom styles for this page -->
<link href="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.css' %}" rel="stylesheet">
<style>
    /* Products page enhancements */
    .products-header {
        background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(0, 198, 255, 0.1) 100%) !important;
        border-radius: 1rem !important;
        padding: 1.5rem !important;
        margin-bottom: 2rem !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .products-table {
        background: rgba(10, 25, 47, 0.8) !important;
        border-radius: 1rem !important;
        overflow: hidden !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(10px) !important;
    }

    .table-responsive {
        border-radius: 1rem !important;
    }

    .btn-add-product {
        background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%) !important;
        border: none !important;
        border-radius: 0.5rem !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 15px rgba(0, 112, 243, 0.3) !important;
    }

    .btn-add-product:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(0, 112, 243, 0.4) !important;
    }

    .table tbody tr:hover {
        background: rgba(0, 112, 243, 0.1) !important;
        transform: scale(1.01) !important;
        transition: all 0.3s ease !important;
    }

    .btn-action {
        border-radius: 0.3rem !important;
        padding: 0.4rem 0.8rem !important;
        font-size: 0.8rem !important;
        transition: all 0.3s ease !important;
    }

    .btn-action:hover {
        transform: translateY(-1px) !important;
    }

    /* DataTables styling */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    .dataTables_wrapper .dataTables_filter input {
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-radius: 0.5rem !important;
    }

    /* Modal enhancements - Complete fix for interaction issues */
    .modal {
        z-index: 9999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: auto !important;
        background-color: rgba(10, 25, 47, 0.8) !important;
    }

    .modal-backdrop {
        display: none !important;
    }

    .modal-dialog {
        z-index: 10000 !important;
        position: relative !important;
        margin: 50px auto !important;
        pointer-events: auto !important;
    }

    .modal-content {
        z-index: 10001 !important;
        position: relative !important;
        pointer-events: auto !important;
        margin: 0 auto !important;
    }

    .modal-content .form-control {
        pointer-events: auto !important;
        z-index: 1080 !important;
        position: relative !important;
    }

    .modal-content .form-control:focus {
        background: rgba(255, 255, 255, 0.1) !important;
        border-color: #0070f3 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 112, 243, 0.25) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        z-index: 1090 !important;
    }

    .modal-content .form-control::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
    }

    .modal-content .input-group-text {
        border-radius: 0.5rem 0 0 0.5rem !important;
        pointer-events: auto !important;
        z-index: 1080 !important;
    }

    .modal-content .input-group .form-control {
        border-radius: 0 0.5rem 0.5rem 0 !important;
    }

    .modal-content label {
        pointer-events: none !important;
        z-index: 1075 !important;
    }

    .modal-content button {
        pointer-events: auto !important;
        z-index: 1080 !important;
        position: relative !important;
    }

    /* Fix any overlay issues */
    .modal-body {
        pointer-events: auto !important;
        z-index: 1070 !important;
    }

    .modal-header {
        pointer-events: auto !important;
        z-index: 1070 !important;
    }

    .modal-footer {
        pointer-events: auto !important;
        z-index: 1070 !important;
    }

    /* Remove any conflicting styles */
    .modal-content::before,
    .modal-content::after {
        pointer-events: none !important;
    }

    /* Force interaction on all modal elements */
    .modal input,
    .modal textarea,
    .modal select,
    .modal button {
        pointer-events: auto !important;
        z-index: 1060 !important;
        position: relative !important;
        -webkit-user-select: auto !important;
        -moz-user-select: auto !important;
        -ms-user-select: auto !important;
        user-select: auto !important;
    }

    /* Disable any overlay that might interfere */
    .modal-dialog {
        pointer-events: auto !important;
    }

    .modal-content {
        pointer-events: auto !important;
    }

    /* Ensure no pseudo-elements block interaction */
    .modal *::before,
    .modal *::after {
        pointer-events: none !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Heading -->
<div class="products-header">
    <div class="d-sm-flex align-items-center justify-content-between">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-box fa-fw mr-2"></i>
                Products
            </h1>
            <p class="mt-2 mb-0" style="color: rgba(255, 255, 255, 0.7);">Manage your product inventory here.</p>
        </div>
        <button type="button" class="btn btn-add-product" onclick="openAddModal()">
            <i class="fas fa-plus-circle fa-sm fa-fw mr-2"></i>Add Product
        </button>
    </div>
</div>

<!-- DataTales Example -->
<div class="card products-table shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold" style="color: white;">
            <i class="fas fa-list fa-fw mr-2"></i>Products List
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Reference</th>
                        <th>Brand</th>
                        <th>Model</th>
                        <th>Price</th>
                        <th>Bulk Price</th>
                        <th>Stock</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Reference</th>
                        <th>Brand</th>
                        <th>Model</th>
                        <th>Price</th>
                        <th>Bulk Price</th>
                        <th>Stock</th>
                        <th>Actions</th>
                    </tr>
                </tfoot>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>{{ product.id }}</td>
                        <td>{{ product.name }}</td>
                        <td>{{ product.reference }}</td>
                        <td>{{ product.brand }}</td>
                        <td>{{ product.model }}</td>
                        <td>${{ product.price }}</td>
                        <td>${{ product.bulk_price }}</td>
                        <td>{{ product.stock_quantity }}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-primary edit-product"
                                data-id="{{ product.id }}"
                                data-name="{{ product.name }}"
                                data-reference="{{ product.reference }}"
                                data-brand="{{ product.brand }}"
                                data-model="{{ product.model }}"
                                data-description="{{ product.description }}"
                                data-price="{{ product.price }}"
                                data-bulk-price="{{ product.bulk_price }}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger delete-product" data-id="{{ product.id }}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Product Modal -->
<div class="modal fade" id="editProductModal" tabindex="-1" role="dialog" aria-labelledby="editProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="background: rgba(10, 25, 47, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 1rem; backdrop-filter: blur(20px);">
            <div class="modal-header" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border-radius: 1rem 1rem 0 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="editProductModalLabel" style="color: white; font-weight: 600;">
                    <i class="fas fa-edit fa-fw mr-2"></i>Edit Product
                </h5>
                <button class="close" type="button" onclick="closeEditModal()" aria-label="Close" style="color: white; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{% url 'product_edit' %}" method="post">
                {% csrf_token %}
                <div class="modal-body" style="background: transparent; color: rgba(255, 255, 255, 0.9);">
                    <input type="hidden" name="id" id="edit-product-id">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="edit-product-name" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-tag fa-fw mr-1"></i>Name
                            </label>
                            <input type="text" class="form-control" id="edit-product-name" name="name" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="edit-product-reference" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-barcode fa-fw mr-1"></i>Reference
                            </label>
                            <input type="text" class="form-control" id="edit-product-reference" name="reference" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="edit-product-brand" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-industry fa-fw mr-1"></i>Brand
                            </label>
                            <input type="text" class="form-control" id="edit-product-brand" name="brand" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="edit-product-model" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-car fa-fw mr-1"></i>Model
                            </label>
                            <input type="text" class="form-control" id="edit-product-model" name="model" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="edit-product-description" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                            <i class="fas fa-align-left fa-fw mr-1"></i>Description
                        </label>
                        <textarea class="form-control" id="edit-product-description" name="description" rows="3" required
                                  style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem; resize: vertical;"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="edit-product-price" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-dollar-sign fa-fw mr-1"></i>Price
                            </label>
                            <input type="number" step="0.01" class="form-control" id="edit-product-price" name="price" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="edit-product-bulk-price" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-tags fa-fw mr-1"></i>Bulk Price
                            </label>
                            <input type="number" step="0.01" class="form-control" id="edit-product-bulk-price" name="bulk_price" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: transparent; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button class="btn btn-secondary" type="button" onclick="closeEditModal()"
                            style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white;">
                        <i class="fas fa-times fa-fw mr-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary"
                            style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border: none; border-radius: 0.5rem; font-weight: 600;">
                        <i class="fas fa-save fa-fw mr-1"></i>Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" role="dialog" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="background: rgba(10, 25, 47, 0.95); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 1rem; backdrop-filter: blur(20px);">
            <div class="modal-header" style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border-radius: 1rem 1rem 0 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                <h5 class="modal-title" id="addProductModalLabel" style="color: white; font-weight: 600;">
                    <i class="fas fa-plus-circle fa-fw mr-2"></i>Add Product
                </h5>
                <button class="close" type="button" onclick="closeAddModal()" aria-label="Close" style="color: white; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{% url 'product_add' %}" method="post">
                {% csrf_token %}
                <div class="modal-body" style="background: transparent; color: rgba(255, 255, 255, 0.9);">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="add-product-name" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-tag fa-fw mr-1"></i>Name
                            </label>
                            <input type="text" class="form-control" id="add-product-name" name="name" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="add-product-reference" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-barcode fa-fw mr-1"></i>Reference
                            </label>
                            <input type="text" class="form-control" id="add-product-reference" name="reference" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="add-product-brand" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-industry fa-fw mr-1"></i>Brand
                            </label>
                            <input type="text" class="form-control" id="add-product-brand" name="brand" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="add-product-model" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-car fa-fw mr-1"></i>Model
                            </label>
                            <input type="text" class="form-control" id="add-product-model" name="model" required
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="add-product-description" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                            <i class="fas fa-align-left fa-fw mr-1"></i>Description
                        </label>
                        <textarea class="form-control" id="add-product-description" name="description" rows="3" required
                                  style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem; resize: vertical;"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label for="add-product-price" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-dollar-sign fa-fw mr-1"></i>Price
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.7);">$</span>
                                </div>
                                <input type="number" step="0.01" class="form-control" id="add-product-price" name="price" required
                                       style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9);">
                            </div>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="add-product-bulk-price" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-tags fa-fw mr-1"></i>Bulk Price
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text" style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.7);">$</span>
                                </div>
                                <input type="number" step="0.01" class="form-control" id="add-product-bulk-price" name="bulk_price" required
                                       style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9);">
                            </div>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="add-product-quantity" style="color: rgba(255, 255, 255, 0.8); font-weight: 600;">
                                <i class="fas fa-boxes fa-fw mr-1"></i>Initial Stock
                            </label>
                            <input type="number" class="form-control" id="add-product-quantity" name="quantity" value="0" min="0"
                                   style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); color: rgba(255, 255, 255, 0.9); border-radius: 0.5rem;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background: transparent; border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <button class="btn btn-secondary" type="button" onclick="closeAddModal()"
                            style="background: rgba(108, 117, 125, 0.8); border: none; border-radius: 0.5rem; color: white;">
                        <i class="fas fa-times fa-fw mr-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary"
                            style="background: linear-gradient(135deg, #0070f3 0%, #00c6ff 100%); border: none; border-radius: 0.5rem; font-weight: 600;">
                        <i class="fas fa-plus-circle fa-fw mr-1"></i>Add Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Product Modal -->
<div class="modal fade" id="deleteProductModal" tabindex="-1" role="dialog" aria-labelledby="deleteProductModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProductModalLabel">Delete Product</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">Are you sure you want to delete this product? This action cannot be undone.</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                <form action="{% url 'product_remove' %}" method="post">
                    {% csrf_token %}
                    <input type="hidden" name="id" id="delete-product-id">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Page level plugins -->
<script src="{% static 'sbadmin/vendor/datatables/jquery.dataTables.min.js' %}"></script>
<script src="{% static 'sbadmin/vendor/datatables/dataTables.bootstrap4.min.js' %}"></script>

<!-- Page level custom scripts -->
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#dataTable').DataTable();

        // Force remove any existing backdrops on page load
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Edit Product
        $('.edit-product').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var reference = $(this).data('reference');
            var brand = $(this).data('brand');
            var model = $(this).data('model');
            var description = $(this).data('description');
            var price = $(this).data('price');
            var bulkPrice = $(this).data('bulk-price');

            openEditModal(id, name, reference, brand, model, description, price, bulkPrice);
        });

        // Delete Product
        $('.delete-product').click(function() {
            var id = $(this).data('id');
            $('#delete-product-id').val(id);
            $('#deleteProductModal').modal('show');
        });

        // Complete fix for modal interaction issues
        $('#addProductModal').on('show.bs.modal', function () {
            // Remove any existing backdrop
            $('.modal-backdrop').remove();

            // Ensure modal is fully interactive
            $(this).css({
                'pointer-events': 'auto',
                'z-index': '9999',
                'position': 'fixed',
                'top': '0',
                'left': '0',
                'width': '100%',
                'height': '100%',
                'background-color': 'rgba(10, 25, 47, 0.8)',
                'overflow': 'auto'
            });
        });

        $('#addProductModal').on('shown.bs.modal', function () {
            // Ensure all form elements are interactive
            $(this).find('input, textarea, select, button').css({
                'pointer-events': 'auto',
                'z-index': '10001',
                'position': 'relative'
            });

            // Focus on first input
            setTimeout(function() {
                $('#add-product-name').focus();
            }, 100);
        });

        // Fix backdrop click for Add Modal
        $('#addProductModal').on('click', function(e) {
            if (e.target === this) {
                $(this).modal('hide');
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }
        });

        // Fix backdrop click for Edit Modal
        $('#editProductModal').on('click', function(e) {
            if (e.target === this) {
                $(this).modal('hide');
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }
        });

        // Ensure proper cleanup when modals are hidden
        $('#addProductModal, #editProductModal').on('hidden.bs.modal', function () {
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');
        });

        // Ensure form submission works
        $('#addProductModal form').on('submit', function(e) {
            // Allow normal form submission
            return true;
        });

        // Fix input focus issues
        $('#addProductModal input, #addProductModal textarea, #addProductModal select').on('focus', function() {
            $(this).css({
                'pointer-events': 'auto',
                'z-index': '1070',
                'position': 'relative'
            });
        });

        // Force enable all inputs on modal show
        $('#addProductModal').on('show.bs.modal', function () {
            $(this).find('input, textarea, select, button').each(function() {
                $(this).prop('disabled', false);
                $(this).css({
                    'pointer-events': 'auto',
                    'z-index': '1060'
                });
            });
        });

        // Fix Edit Product Modal interaction issues
        $('#editProductModal').on('show.bs.modal', function () {
            // Remove any existing backdrop
            $('.modal-backdrop').remove();

            // Ensure modal is fully interactive
            $(this).css({
                'pointer-events': 'auto',
                'z-index': '9999',
                'position': 'fixed',
                'top': '0',
                'left': '0',
                'width': '100%',
                'height': '100%',
                'background-color': 'rgba(10, 25, 47, 0.8)',
                'overflow': 'auto'
            });
        });

        $('#editProductModal').on('shown.bs.modal', function () {
            // Ensure all form elements are interactive
            $(this).find('input, textarea, select, button').css({
                'pointer-events': 'auto',
                'z-index': '10001',
                'position': 'relative'
            });

            // Focus on first input
            setTimeout(function() {
                $('#edit-product-name').focus();
            }, 100);
        });

        // Force enable all inputs on edit modal show
        $('#editProductModal').on('show.bs.modal', function () {
            $(this).find('input, textarea, select, button').each(function() {
                $(this).prop('disabled', false);
                $(this).css({
                    'pointer-events': 'auto',
                    'z-index': '10001'
                });
            });
        });

        // Add click handlers for modal buttons
        $(document).on('click', '[data-target="#addProductModal"]', function(e) {
            e.preventDefault();
            $('.modal-backdrop').remove();
            $('#addProductModal').modal('show');
        });

        // Ensure close buttons work
        $(document).on('click', '.modal .close, .modal [data-dismiss="modal"]', function(e) {
            e.preventDefault();
            $(this).closest('.modal').modal('hide');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        });
    });

    // Custom modal functions
    function openAddModal() {
        // Remove any existing backdrop
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Show modal with custom method
        $('#addProductModal').css({
            'display': 'block',
            'z-index': '9999',
            'position': 'fixed',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(10, 25, 47, 0.8)',
            'overflow': 'auto'
        }).addClass('show');

        // Ensure all elements are interactive
        $('#addProductModal input, #addProductModal textarea, #addProductModal button').css({
            'pointer-events': 'auto',
            'z-index': '10001'
        });

        // Focus first input
        setTimeout(function() {
            $('#add-product-name').focus();
        }, 100);
    }

    function closeAddModal() {
        $('#addProductModal').css('display', 'none').removeClass('show');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    }

    function openEditModal(id, name, reference, brand, model, description, price, bulkPrice) {
        // Remove any existing backdrop
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');

        // Fill form data
        $('#edit-product-id').val(id);
        $('#edit-product-name').val(name);
        $('#edit-product-reference').val(reference);
        $('#edit-product-brand').val(brand);
        $('#edit-product-model').val(model);
        $('#edit-product-description').val(description);
        $('#edit-product-price').val(price);
        $('#edit-product-bulk-price').val(bulkPrice);

        // Show modal with custom method
        $('#editProductModal').css({
            'display': 'block',
            'z-index': '9999',
            'position': 'fixed',
            'top': '0',
            'left': '0',
            'width': '100%',
            'height': '100%',
            'background-color': 'rgba(10, 25, 47, 0.8)',
            'overflow': 'auto'
        }).addClass('show');

        // Ensure all elements are interactive
        $('#editProductModal input, #editProductModal textarea, #editProductModal button').css({
            'pointer-events': 'auto',
            'z-index': '10001'
        });

        // Focus first input
        setTimeout(function() {
            $('#edit-product-name').focus();
        }, 100);
    }

    function closeEditModal() {
        $('#editProductModal').css('display', 'none').removeClass('show');
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    }
</script>
{% endblock %}
