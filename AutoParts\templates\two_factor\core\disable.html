{% extends 'base.html' %}
{% load static %}
{% load custom_i18n %}

{% block title %}{% custom_trans "Disable Two-Factor Authentication" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Disable Two-Factor Authentication" %}
        </h1>
        <a href="{% url 'profile_view' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> {% custom_trans "Back to Profile" %}
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-8 col-lg-10">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle fa-fw mr-2"></i>{% custom_trans "Security Warning" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-shield-alt fa-5x text-warning mb-3"></i>
                        <h4 class="text-gray-900">{% custom_trans "Disable Two-Factor Authentication" %}</h4>
                        <p class="text-gray-600">
                            {% custom_trans "You are about to disable two-factor authentication for your account." %}
                        </p>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle fa-fw mr-2"></i>
                        <strong>{% custom_trans "Warning:" %}</strong> 
                        {% custom_trans "Disabling two-factor authentication will make your account less secure and more vulnerable to unauthorized access." %}
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-danger shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                {% custom_trans "Security Risk" %}
                                            </div>
                                            <div class="text-xs mb-0 text-gray-800">
                                                {% custom_trans "Your account will be less protected" %}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                {% custom_trans "Data Loss" %}
                                            </div>
                                            <div class="text-xs mb-0 text-gray-800">
                                                {% custom_trans "All backup tokens will be deleted" %}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-trash fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h5 class="card-title text-gray-900">
                                <i class="fas fa-info-circle fa-fw mr-2"></i>{% custom_trans "What will happen:" %}
                            </h5>
                            <ul class="text-gray-700 mb-0">
                                <li>{% custom_trans "Your authenticator app will no longer be required for login" %}</li>
                                <li>{% custom_trans "All backup tokens will be permanently deleted" %}</li>
                                <li>{% custom_trans "You will only need your username and password to access your account" %}</li>
                                <li>{% custom_trans "You can re-enable two-factor authentication at any time" %}</li>
                            </ul>
                        </div>
                    </div>

                    {% if form %}
                        <form method="post">
                            {% csrf_token %}
                            
                            {% if form.password %}
                                <div class="form-group">
                                    <label for="{{ form.password.id_for_label }}" class="font-weight-bold">
                                        <i class="fas fa-key fa-fw mr-2"></i>{% custom_trans "Confirm your password to continue" %}
                                    </label>
                                    <input type="password" class="form-control" 
                                           id="{{ form.password.id_for_label }}"
                                           name="{{ form.password.name }}"
                                           placeholder="{% custom_trans 'Enter your current password' %}" required>
                                    {% if form.password.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.password.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            {% endif %}

                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="confirmDisable" required>
                                <label class="form-check-label" for="confirmDisable">
                                    {% custom_trans "I understand the security risks and want to disable two-factor authentication" %}
                                </label>
                            </div>

                            <div class="text-center">
                                <a href="{% url 'profile_view' %}" class="btn btn-secondary btn-lg mr-3">
                                    <i class="fas fa-times fa-fw mr-2"></i>{% custom_trans "Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-warning btn-lg" id="disableBtn" disabled>
                                    <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Disable 2FA" %}
                                </button>
                            </div>
                        </form>
                    {% else %}
                        <!-- Simple confirmation form if no password required -->
                        <form method="post">
                            {% csrf_token %}
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="confirmDisable" required>
                                <label class="form-check-label" for="confirmDisable">
                                    {% custom_trans "I understand the security risks and want to disable two-factor authentication" %}
                                </label>
                            </div>

                            <div class="text-center">
                                <a href="{% url 'profile_view' %}" class="btn btn-secondary btn-lg mr-3">
                                    <i class="fas fa-times fa-fw mr-2"></i>{% custom_trans "Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-warning btn-lg" id="disableBtn" disabled>
                                    <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Disable 2FA" %}
                                </button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>

            <!-- Alternative: Keep 2FA Enabled -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-shield-alt fa-fw mr-2"></i>{% custom_trans "Recommended: Keep 2FA Enabled" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-success shadow h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-success">
                                        <i class="fas fa-cog fa-fw mr-2"></i>{% custom_trans "Manage Settings" %}
                                    </h5>
                                    <p class="card-text text-gray-600">
                                        {% custom_trans "Generate new backup tokens or manage your 2FA settings instead." %}
                                    </p>
                                    <a href="{% url 'two_factor:backup_tokens' %}" class="btn btn-success">
                                        <i class="fas fa-key fa-fw mr-2"></i>{% custom_trans "Manage Tokens" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-info shadow h-100">
                                <div class="card-body">
                                    <h5 class="card-title text-info">
                                        <i class="fas fa-question-circle fa-fw mr-2"></i>{% custom_trans "Need Help?" %}
                                    </h5>
                                    <p class="card-text text-gray-600">
                                        {% custom_trans "If you're having trouble with 2FA, we can help you troubleshoot." %}
                                    </p>
                                    <a href="{% url 'settings_view' %}" class="btn btn-info">
                                        <i class="fas fa-life-ring fa-fw mr-2"></i>{% custom_trans "Get Help" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 0.5rem;
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    border-radius: 0.5rem;
}

#disableBtn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkbox = document.getElementById('confirmDisable');
    const disableBtn = document.getElementById('disableBtn');
    
    checkbox.addEventListener('change', function() {
        disableBtn.disabled = !this.checked;
    });
});
</script>
{% endblock %}
